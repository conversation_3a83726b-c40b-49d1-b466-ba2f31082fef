{"accessibility": {"captions": {"live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 0}, "autocomplete": {"retention_policy_last_version": 133}, "autofill": {"last_version_deduped": 133}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 776, "left": 214, "maximized": true, "right": 1672, "top": 280, "work_area_bottom": 912, "work_area_left": 0, "work_area_right": 1440, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21843, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "d5d499c6-58d5-44d0-8527-1f7f9cb7f456", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "133.0.6943.142"}, "gaia_cookie": {"changed_time": **********.231227, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "024b7c3c-e850-44bf-928f-b523cabbaa2b"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["***********878630", "*****************", "*****************", "*****************"], "session_last_active_time": "*****************", "session_start_time": "***********878630"}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "media": {"device_id_salt": "94AE38AE578BA9258DDDF5A552B52CF9", "engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "Hb6zcLcpDvvVV7zsRq2EtLEXmLIpXau9E3eaDEmmsAbz6XLfNwp/TA4SuywPJ16zAIadc58rGq2fw+RpMkXErQ=="}, "ntp": {"num_personal_suggestions": 2}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "13393601009995960", "last_fetch_success": "13393601010367381"}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "EWALLET_MERCHANT_ALLOWLIST": true, "FORMS_ANNOTATIONS": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "PAGE_ENTITIES": true, "PRICE_INSIGHTS": true, "PRICE_TRACKING": true, "SALIENT_IMAGE": true, "SHOPPING_DISCOUNTS": true, "SHOPPING_PAGE_TYPES": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {"http://localhost:59232,*": {"last_modified": "*****************", "setting": {"http://localhost:59232/": {"couldShowBannerEvents": 1.339360100073937e+16, "next_install_text_animation": {"delay": "***********", "last_shown": "*****************"}}}}}, "ar": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "media_engagement": {"http://localhost:51940,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:52285,*": {"expiration": "13401295628369524", "last_modified": "13393519628369536", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:52376,*": {"expiration": "13401375866654019", "last_modified": "13393599866654027", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:52888,*": {"expiration": "13401293078221324", "last_modified": "13393517078221337", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:53878,*": {"expiration": "13401294563923413", "last_modified": "13393518563923424", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:57925,*": {"expiration": "13401290450176848", "last_modified": "13393514450176862", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:58910,*": {"expiration": "13401318154947752", "last_modified": "13393542154947763", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:59232,*": {"expiration": "13401377107235961", "last_modified": "13393601107235975", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:59533,*": {"expiration": "13401376109432110", "last_modified": "13393600109432125", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:59698,*": {"expiration": "13401296178460331", "last_modified": "13393520178460343", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:61782,*": {"expiration": "13401284548183871", "last_modified": "13393508548183894", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:63375,*": {"expiration": "13401250952615892", "last_modified": "13393474952615906", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:65428,*": {"expiration": "13401294259629868", "last_modified": "13393518259629877", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {"http://localhost:51940,*": {"last_modified": "13393506294466653", "last_visit": "13393296000000000", "setting": 1}, "http://localhost:63375,*": {"last_modified": "13393474808535963", "last_visit": "13393296000000000", "setting": 1}}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"http://localhost:51940,*": {"last_modified": "13393599711049147", "setting": {"lastEngagementTime": 1.3393535280746488e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 15.0}}, "http://localhost:52285,*": {"last_modified": "13393599711049118", "setting": {"lastEngagementTime": 1.3393548386026742e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.6}}, "http://localhost:52376,*": {"last_modified": "13393599858307144", "setting": {"lastEngagementTime": 1.3393599858307104e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.999999999999999, "rawScore": 5.999999999999999}}, "http://localhost:52888,*": {"last_modified": "13393599711049083", "setting": {"lastEngagementTime": 1.3393545834352372e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.2}}, "http://localhost:53878,*": {"last_modified": "13393599711049044", "setting": {"lastEngagementTime": 1.3393547322356728e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.2}}, "http://localhost:57925,*": {"last_modified": "13393599711049007", "setting": {"lastEngagementTime": 1.3393543205476576e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 15.0}}, "http://localhost:58910,*": {"last_modified": "13393599711048971", "setting": {"lastEngagementTime": 1.33935709110485e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.6}}, "http://localhost:59232,*": {"last_modified": "13393601097730027", "setting": {"lastEngagementTime": 1.3393601097729992e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.2, "rawScore": 4.2}}, "http://localhost:59533,*": {"last_modified": "13393600105473980", "setting": {"lastEngagementTime": 1.3393600105473952e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 8.999999999999998, "rawScore": 8.999999999999998}}, "http://localhost:59698,*": {"last_modified": "13393599711048931", "setting": {"lastEngagementTime": 1.339354894011137e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.3999999999999995}}, "http://localhost:61782,*": {"last_modified": "13393599711048890", "setting": {"lastEngagementTime": 1.3393537100059374e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 15.0}}, "http://localhost:63375,*": {"last_modified": "13393599711048843", "setting": {"lastEngagementTime": 1.3393503706823992e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 10.799999999999997, "rawScore": 10.799999999999997}}, "http://localhost:65428,*": {"last_modified": "13393599711048673", "setting": {"lastEngagementTime": 1.3393546804272444e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "permission_actions": {"mic_stream": [{"action": 0, "prompt_disposition": 12, "time": "13393474808554340"}, {"action": 0, "prompt_disposition": 12, "time": "13393506294481790"}]}, "pref_version": 1}, "created_by_version": "133.0.6943.142", "creation_time": "13393474753721894", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "*****************", "last_time_obsolete_http_credentials_removed": **********.788633, "last_time_password_store_metrics_reported": **********.850509, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Person 1", "one_time_permission_prompts_decided_count": 2, "password_account_storage_settings": {}, "password_hash_data_list": [], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "*****************", "hash_real_time_ohttp_key": "1AAgYy1g9w1EQMpR8lYEeiU6qnH563jdDaeSuU56uMpoxicABAABAAI=", "metrics_last_log_time": "***********", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "CuUCChFjcm9zc19kZXZpY2VfdXNlchLPAgrDAg0AAIA/EOb084qHreUXGrACCqcCGqQCChkNAACAPxISTm9Dcm9zc0RldmljZVVzYWdlChgNAAAAQBIRQ3Jvc3NEZXZpY2VNb2JpbGUKGQ0AAEBAEhJDcm9zc0RldmljZURlc2t0b3AKGA0AAIBAEhFDcm9zc0RldmljZVRhYmxldAoiDQAAoEASG0Nyb3NzRGV2aWNlTW9iaWxlQW5kRGVza3RvcAohDQAAwEASGkNyb3NzRGV2aWNlTW9iaWxlQW5kVGFibGV0CiINAADgQBIbQ3Jvc3NEZXZpY2VEZXNrdG9wQW5kVGFibGV0CiANAAAAQRIZQ3Jvc3NEZXZpY2VBbGxEZXZpY2VUeXBlcwoXDQAAEEESEENyb3NzRGV2aWNlT3RoZXISEk5vQ3Jvc3NEZXZpY2VVc2FnZRIEEAcYBCACELn984qHreUXClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQvPLwyrWp5RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEPTz8Mq1qeUX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13393468799000000", "uma_in_sql_start_time": "13393474753782047"}, "sessions": {"event_log": [{"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393514450157664", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393517037804761", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393517078203292", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393518038677720", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393518259599379", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393518369669232", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393518563907518", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393519521810047", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393519628344168", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393520108751899", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393520178443549", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393542118042324", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393542154931850", "type": 2, "window_count": 1}, {"crashed": false, "time": "***********846815", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393599866638708", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393599983576761", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393600109412017", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393600999997632", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393601107215879", "type": 2, "window_count": 1}], "session_data_status": 3}, "settings": {"force_google_safesearch": false}, "should_read_incoming_syncing_theme_prefs": true, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "133"}}