{"logs": [{"outputFile": "io.flutter.plugins.sharedpreferences.shared_preferences_android-mergeReleaseResources-38:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,265,348,490,659,744", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "173,260,343,485,654,739,819"}, "to": {"startLines": "36,37,38,39,42,43,44", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3621,3694,3781,3864,4192,4361,4446", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "3689,3776,3859,4001,4356,4441,4521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "29,30,31,32,33,34,35,41", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2886,2984,3087,3187,3290,3398,3504,4091", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "2979,3082,3182,3285,3393,3499,3616,4187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b4c7ccc4495020ec151e51318a1fa1ad\\transformed\\appcompat-1.1.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,940,1031,1123,1218,1312,1413,1506,1601,1698,1789,1880,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,85,90,91,94,93,100,92,94,96,90,90,83,110,108,101,110,109,107,170,99,84", "endOffsets": "218,329,446,531,637,760,849,935,1026,1118,1213,1307,1408,1501,1596,1693,1784,1875,1959,2070,2179,2281,2392,2502,2610,2781,2881,2966"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,940,1031,1123,1218,1312,1413,1506,1601,1698,1789,1880,1964,2075,2184,2286,2397,2507,2615,2786,4006", "endColumns": "117,110,116,84,105,122,88,85,90,91,94,93,100,92,94,96,90,90,83,110,108,101,110,109,107,170,99,84", "endOffsets": "218,329,446,531,637,760,849,935,1026,1118,1213,1307,1408,1501,1596,1693,1784,1875,1959,2070,2179,2281,2392,2502,2610,2781,2881,4086"}}]}, {"outputFile": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,265,348,490,659,744", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "173,260,343,485,654,739,819"}, "to": {"startLines": "36,37,38,39,42,43,44", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3621,3694,3781,3864,4192,4361,4446", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "3689,3776,3859,4001,4356,4441,4521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "29,30,31,32,33,34,35,41", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2886,2984,3087,3187,3290,3398,3504,4091", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "2979,3082,3182,3285,3393,3499,3616,4187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b4c7ccc4495020ec151e51318a1fa1ad\\transformed\\appcompat-1.1.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,940,1031,1123,1218,1312,1413,1506,1601,1698,1789,1880,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,85,90,91,94,93,100,92,94,96,90,90,83,110,108,101,110,109,107,170,99,84", "endOffsets": "218,329,446,531,637,760,849,935,1026,1118,1213,1307,1408,1501,1596,1693,1784,1875,1959,2070,2179,2281,2392,2502,2610,2781,2881,2966"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,940,1031,1123,1218,1312,1413,1506,1601,1698,1789,1880,1964,2075,2184,2286,2397,2507,2615,2786,4006", "endColumns": "117,110,116,84,105,122,88,85,90,91,94,93,100,92,94,96,90,90,83,110,108,101,110,109,107,170,99,84", "endOffsets": "218,329,446,531,637,760,849,935,1026,1118,1213,1307,1408,1501,1596,1693,1784,1875,1959,2070,2179,2281,2392,2502,2610,2781,2881,4086"}}]}]}