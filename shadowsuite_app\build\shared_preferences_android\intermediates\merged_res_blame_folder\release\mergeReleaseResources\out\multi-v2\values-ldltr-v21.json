{"logs": [{"outputFile": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/values-ldltr-v21/values-ldltr-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b4c7ccc4495020ec151e51318a1fa1ad\\transformed\\appcompat-1.1.0\\res\\values-ldltr-v21\\values-ldltr-v21.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "112", "endOffsets": "163"}}]}, {"outputFile": "io.flutter.plugins.sharedpreferences.shared_preferences_android-mergeReleaseResources-38:/values-ldltr-v21/values-ldltr-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b4c7ccc4495020ec151e51318a1fa1ad\\transformed\\appcompat-1.1.0\\res\\values-ldltr-v21\\values-ldltr-v21.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "112", "endOffsets": "163"}}]}]}