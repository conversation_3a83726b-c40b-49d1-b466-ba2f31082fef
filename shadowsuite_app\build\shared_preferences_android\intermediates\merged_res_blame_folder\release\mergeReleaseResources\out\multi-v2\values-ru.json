{"logs": [{"outputFile": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "29,30,31,32,33,34,35,41", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2822,2920,3022,3123,3224,3329,3432,4018", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "2915,3017,3118,3219,3324,3427,3544,4114"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b4c7ccc4495020ec151e51318a1fa1ad\\transformed\\appcompat-1.1.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,889,981,1074,1169,1262,1358,1452,1548,1643,1735,1827,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,76,91,92,94,92,95,93,95,94,91,91,87,105,106,97,108,106,113,165,99,80", "endOffsets": "215,317,416,502,607,728,807,884,976,1069,1164,1257,1353,1447,1543,1638,1730,1822,1910,2016,2123,2221,2330,2437,2551,2717,2817,2898"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,889,981,1074,1169,1262,1358,1452,1548,1643,1735,1827,1915,2021,2128,2226,2335,2442,2556,2722,3937", "endColumns": "114,101,98,85,104,120,78,76,91,92,94,92,95,93,95,94,91,91,87,105,106,97,108,106,113,165,99,80", "endOffsets": "215,317,416,502,607,728,807,884,976,1069,1164,1257,1353,1447,1543,1638,1730,1822,1910,2016,2123,2221,2330,2437,2551,2717,2817,4013"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,271,348,493,662,744", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "174,266,343,488,657,739,817"}, "to": {"startLines": "36,37,38,39,42,43,44", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3549,3623,3715,3792,4119,4288,4370", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "3618,3710,3787,3932,4283,4365,4443"}}]}, {"outputFile": "io.flutter.plugins.sharedpreferences.shared_preferences_android-mergeReleaseResources-38:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "29,30,31,32,33,34,35,41", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2822,2920,3022,3123,3224,3329,3432,4018", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "2915,3017,3118,3219,3324,3427,3544,4114"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b4c7ccc4495020ec151e51318a1fa1ad\\transformed\\appcompat-1.1.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,889,981,1074,1169,1262,1358,1452,1548,1643,1735,1827,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,76,91,92,94,92,95,93,95,94,91,91,87,105,106,97,108,106,113,165,99,80", "endOffsets": "215,317,416,502,607,728,807,884,976,1069,1164,1257,1353,1447,1543,1638,1730,1822,1910,2016,2123,2221,2330,2437,2551,2717,2817,2898"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,889,981,1074,1169,1262,1358,1452,1548,1643,1735,1827,1915,2021,2128,2226,2335,2442,2556,2722,3937", "endColumns": "114,101,98,85,104,120,78,76,91,92,94,92,95,93,95,94,91,91,87,105,106,97,108,106,113,165,99,80", "endOffsets": "215,317,416,502,607,728,807,884,976,1069,1164,1257,1353,1447,1543,1638,1730,1822,1910,2016,2123,2221,2330,2437,2551,2717,2817,4013"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,271,348,493,662,744", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "174,266,343,488,657,739,817"}, "to": {"startLines": "36,37,38,39,42,43,44", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3549,3623,3715,3792,4119,4288,4370", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "3618,3710,3787,3932,4283,4365,4443"}}]}]}