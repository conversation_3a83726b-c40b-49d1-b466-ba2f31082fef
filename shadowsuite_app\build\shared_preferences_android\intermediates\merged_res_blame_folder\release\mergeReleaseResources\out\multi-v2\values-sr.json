{"logs": [{"outputFile": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b4c7ccc4495020ec151e51318a1fa1ad\\transformed\\appcompat-1.1.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,897,988,1080,1175,1269,1370,1463,1558,1663,1754,1845,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,81,90,91,94,93,100,92,94,104,90,90,84,104,105,102,105,108,106,169,96,85", "endOffsets": "207,308,414,500,604,726,810,892,983,1075,1170,1264,1365,1458,1553,1658,1749,1840,1925,2030,2136,2239,2345,2454,2561,2731,2828,2914"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,897,988,1080,1175,1269,1370,1463,1558,1663,1754,1845,1930,2035,2141,2244,2350,2459,2566,2736,3949", "endColumns": "106,100,105,85,103,121,83,81,90,91,94,93,100,92,94,104,90,90,84,104,105,102,105,108,106,169,96,85", "endOffsets": "207,308,414,500,604,726,810,892,983,1075,1170,1264,1365,1458,1553,1658,1749,1840,1925,2030,2136,2239,2345,2454,2561,2731,2828,4030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,343,495,664,750", "endColumns": "70,86,79,151,168,85,81", "endOffsets": "171,258,338,490,659,745,827"}, "to": {"startLines": "36,37,38,39,42,43,44", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3559,3630,3717,3797,4136,4305,4391", "endColumns": "70,86,79,151,168,85,81", "endOffsets": "3625,3712,3792,3944,4300,4386,4468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "29,30,31,32,33,34,35,41", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2833,2931,3033,3130,3234,3338,3443,4035", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "2926,3028,3125,3229,3333,3438,3554,4131"}}]}, {"outputFile": "io.flutter.plugins.sharedpreferences.shared_preferences_android-mergeReleaseResources-38:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b4c7ccc4495020ec151e51318a1fa1ad\\transformed\\appcompat-1.1.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,897,988,1080,1175,1269,1370,1463,1558,1663,1754,1845,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,81,90,91,94,93,100,92,94,104,90,90,84,104,105,102,105,108,106,169,96,85", "endOffsets": "207,308,414,500,604,726,810,892,983,1075,1170,1264,1365,1458,1553,1658,1749,1840,1925,2030,2136,2239,2345,2454,2561,2731,2828,2914"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,897,988,1080,1175,1269,1370,1463,1558,1663,1754,1845,1930,2035,2141,2244,2350,2459,2566,2736,3949", "endColumns": "106,100,105,85,103,121,83,81,90,91,94,93,100,92,94,104,90,90,84,104,105,102,105,108,106,169,96,85", "endOffsets": "207,308,414,500,604,726,810,892,983,1075,1170,1264,1365,1458,1553,1658,1749,1840,1925,2030,2136,2239,2345,2454,2561,2731,2828,4030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,343,495,664,750", "endColumns": "70,86,79,151,168,85,81", "endOffsets": "171,258,338,490,659,745,827"}, "to": {"startLines": "36,37,38,39,42,43,44", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3559,3630,3717,3797,4136,4305,4391", "endColumns": "70,86,79,151,168,85,81", "endOffsets": "3625,3712,3792,3944,4300,4386,4468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "29,30,31,32,33,34,35,41", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2833,2931,3033,3130,3234,3338,3443,4035", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "2926,3028,3125,3229,3333,3438,3554,4131"}}]}]}