[{"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/drawable-ldpi-v4/ic_call_answer.png", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-core-1.13.1-13:/drawable-ldpi-v4/ic_call_answer.png"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/drawable-ldpi-v4/ic_call_answer_low.png", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-core-1.13.1-13:/drawable-ldpi-v4/ic_call_answer_low.png"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/drawable-ldpi-v4/ic_call_answer_video_low.png", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-core-1.13.1-13:/drawable-ldpi-v4/ic_call_answer_video_low.png"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/drawable-ldpi-v4/ic_call_answer_video.png", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-core-1.13.1-13:/drawable-ldpi-v4/ic_call_answer_video.png"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/drawable-ldpi-v4/ic_call_decline.png", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-core-1.13.1-13:/drawable-ldpi-v4/ic_call_decline.png"}, {"merged": "io.flutter.plugins.sharedpreferences.shared_preferences_android-release-40:/drawable-ldpi-v4/ic_call_decline_low.png", "source": "io.flutter.plugins.sharedpreferences.shared_preferences_android-core-1.13.1-13:/drawable-ldpi-v4/ic_call_decline_low.png"}]