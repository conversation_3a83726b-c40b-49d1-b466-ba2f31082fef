<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="/">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="ShadowSuite - Comprehensive productivity app with 8 mini-apps">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="ShadowSuite">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>ShadowSuite - Productivity Suite</title>
  <link rel="manifest" href="manifest.json">

  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      font-family: 'Roboto', sans-serif;
    }

    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #ffffff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 4px solid #e0e0e0;
      border-top: 4px solid #6200ea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-text {
      margin-top: 20px;
      color: #666;
      font-size: 16px;
    }

    .error-container {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #ffffff;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      padding: 20px;
      box-sizing: border-box;
    }

    .error-icon {
      font-size: 64px;
      color: #f44336;
      margin-bottom: 20px;
    }

    .error-title {
      font-size: 24px;
      color: #333;
      margin-bottom: 10px;
      text-align: center;
    }

    .error-message {
      font-size: 16px;
      color: #666;
      text-align: center;
      max-width: 600px;
      line-height: 1.5;
    }

    .retry-button {
      margin-top: 20px;
      padding: 12px 24px;
      background-color: #6200ea;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
    }

    .retry-button:hover {
      background-color: #3700b3;
    }
  </style>
</head>
<body>
  <!-- Loading Screen -->
  <div id="loading" class="loading-container">
    <div class="loading-spinner"></div>
    <div class="loading-text">Loading ShadowSuite...</div>
  </div>

  <!-- Error Screen -->
  <div id="error" class="error-container">
    <div class="error-icon">⚠️</div>
    <div class="error-title">Failed to Load Application</div>
    <div class="error-message" id="error-message">
      There was an error loading the application. Please check your internet connection and try again.
    </div>
    <button class="retry-button" onclick="location.reload()">Retry</button>
  </div>

  <script>
    // Error handling
    window.addEventListener('error', function(e) {
      console.error('Global error:', e.error);
      showError('JavaScript Error: ' + e.message);
    });

    window.addEventListener('unhandledrejection', function(e) {
      console.error('Unhandled promise rejection:', e.reason);
      showError('Promise Rejection: ' + e.reason);
    });

    function showError(message) {
      document.getElementById('loading').style.display = 'none';
      document.getElementById('error-message').textContent = message;
      document.getElementById('error').style.display = 'flex';
    }

    function hideLoading() {
      document.getElementById('loading').style.display = 'none';
    }

    // Set a timeout to show error if loading takes too long
    setTimeout(function() {
      if (document.getElementById('loading').style.display !== 'none') {
        showError('Application is taking too long to load. Please check your internet connection and try again.');
      }
    }, 30000); // 30 seconds timeout
  </script>

  <script src="flutter_bootstrap.js" async></script>

  <script>
    // Hide loading screen when Flutter is ready
    window.addEventListener('flutter-first-frame', function() {
      hideLoading();
    });
  </script>
</body>
</html>
