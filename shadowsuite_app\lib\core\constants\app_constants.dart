class AppConstants {
  // App Information
  static const String appName = 'ShadowSuite';
  static const String appVersion = '1.0.0';
  static const String appDescription =
      'A comprehensive productivity suite with 8 mini-apps';

  // Design Constants
  static const double defaultPadding = 16.0;
  static const double defaultMargin = 12.0;
  static const double borderRadius = 8.0;
  static const double cardElevation = 2.0;

  // Database Constants
  static const String databaseName = 'shadowsuite.db';
  static const int databaseVersion = 2; // Updated for schema fixes

  // Supabase Configuration
  static const String supabaseUrl = 'https://shadowsuite.supabase.co';
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNoYWRvd3N1aXRlIiwicm9sZSI6ImFub24iLCJpYXQiOjE2ODk1NzYwMDAsImV4cCI6MjAwNTEzNjAwMH0.demo_key_for_development';

  // Mini-App IDs
  static const String memoSuiteId = 'memo_suite';
  static const String athkarProId = 'athkar_pro';
  static const String moneyFlowId = 'money_flow';
  static const String toolsBuilderId = 'tools_builder';
  static const String stocktakingProId = 'stocktaking_pro';
  static const String pcRemotePlusId = 'pc_remote_plus';
  static const String fileSuiteId = 'file_suite';
  static const String timelogId = 'timelog';

  // Permissions
  static const List<String> requiredPermissions = [
    'camera',
    'microphone',
    'storage',
    'location',
    'notification',
  ];

  // File Extensions
  static const List<String> audioExtensions = ['.mp3', '.wav', '.m4a', '.aac'];
  static const List<String> imageExtensions = [
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.bmp',
  ];
  static const List<String> documentExtensions = [
    '.pdf',
    '.doc',
    '.docx',
    '.txt',
    '.rtf',
  ];

  // Network Constants
  static const int connectionTimeout = 30000;
  static const int receiveTimeout = 30000;

  // Cache Constants
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  static const Duration cacheExpiry = Duration(hours: 24);

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
}
