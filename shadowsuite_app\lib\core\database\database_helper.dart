import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../constants/app_constants.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, AppConstants.databaseName);

    return await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // User Profile Table
    await db.execute('''
      CREATE TABLE user_profiles (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT,
        avatar_url TEXT,
        preferences TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        synced_at INTEGER
      )
    ''');

    // Memo Suite Tables
    await db.execute('''
      CREATE TABLE memos (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT,
        audio_path TEXT,
        transcription TEXT,
        tags TEXT,
        type TEXT DEFAULT 'text',
        color INTEGER,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        synced_at INTEGER
      )
    ''');

    // Athkar Pro Tables
    await db.execute('''
      CREATE TABLE routines (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        type TEXT NOT NULL DEFAULT 'daily',
        status TEXT NOT NULL DEFAULT 'active',
        target_count INTEGER NOT NULL DEFAULT 1,
        current_count INTEGER NOT NULL DEFAULT 0,
        start_date INTEGER NOT NULL,
        end_date INTEGER,
        tags TEXT,
        notes TEXT,
        is_notification_enabled INTEGER NOT NULL DEFAULT 0,
        notification_times TEXT,
        streak_count INTEGER NOT NULL DEFAULT 0,
        last_completed_at INTEGER,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        synced_at INTEGER
      )
    ''');

    await db.execute('''
      CREATE TABLE multi_step_routines (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        category TEXT NOT NULL DEFAULT 'general',
        color INTEGER,
        is_sequential INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        synced_at INTEGER
      )
    ''');

    await db.execute('''
      CREATE TABLE athkar_steps (
        id TEXT PRIMARY KEY,
        routine_id TEXT NOT NULL,
        template_id TEXT,
        arabic_text TEXT NOT NULL,
        transliteration TEXT NOT NULL,
        translation TEXT NOT NULL,
        target_count INTEGER NOT NULL,
        current_count INTEGER NOT NULL DEFAULT 0,
        is_completed INTEGER NOT NULL DEFAULT 0,
        completed_at INTEGER,
        step_order INTEGER NOT NULL DEFAULT 0,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (routine_id) REFERENCES multi_step_routines (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE athkar_progress (
        id TEXT PRIMARY KEY,
        routine_id TEXT NOT NULL,
        date TEXT NOT NULL,
        completed_count INTEGER NOT NULL DEFAULT 0,
        total_count INTEGER NOT NULL,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (routine_id) REFERENCES routines (id)
      )
    ''');

    // Money Flow Tables
    await db.execute('''
      CREATE TABLE accounts (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        balance REAL NOT NULL DEFAULT 0.0,
        currency TEXT NOT NULL DEFAULT 'USD',
        description TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        synced_at INTEGER
      )
    ''');

    await db.execute('''
      CREATE TABLE categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        description TEXT,
        color TEXT,
        icon TEXT,
        parent_id TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        synced_at INTEGER
      )
    ''');

    await db.execute('''
      CREATE TABLE transactions (
        id TEXT PRIMARY KEY,
        account_id TEXT NOT NULL,
        category_id TEXT,
        amount REAL NOT NULL,
        description TEXT,
        date INTEGER NOT NULL,
        type TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        synced_at INTEGER,
        FOREIGN KEY (account_id) REFERENCES accounts (id),
        FOREIGN KEY (category_id) REFERENCES categories (id)
      )
    ''');

    // Tools Builder Tables
    await db.execute('''
      CREATE TABLE custom_tools (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        formula TEXT NOT NULL,
        variables TEXT,
        category TEXT,
        is_favorite INTEGER NOT NULL DEFAULT 0,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        synced_at INTEGER
      )
    ''');

    // Stocktaking Pro Tables
    await db.execute('''
      CREATE TABLE stores (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        address TEXT,
        manager TEXT,
        phone TEXT,
        email TEXT,
        description TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        synced_at INTEGER
      )
    ''');

    await db.execute('''
      CREATE TABLE products (
        id TEXT PRIMARY KEY,
        store_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        sku TEXT,
        barcode TEXT,
        category TEXT,
        price REAL,
        cost REAL,
        unit_price REAL,
        quantity INTEGER NOT NULL DEFAULT 0,
        current_stock INTEGER NOT NULL DEFAULT 0,
        min_quantity INTEGER DEFAULT 0,
        min_stock INTEGER DEFAULT 0,
        max_stock INTEGER,
        expiry_date INTEGER,
        image_path TEXT,
        location TEXT,
        supplier TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        synced_at INTEGER,
        FOREIGN KEY (store_id) REFERENCES stores (id)
      )
    ''');

    // File Suite Tables
    await db.execute('''
      CREATE TABLE file_bookmarks (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        path TEXT NOT NULL,
        type TEXT NOT NULL,
        created_at INTEGER NOT NULL
      )
    ''');

    // TimeLog Tables
    await db.execute('''
      CREATE TABLE time_entries (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        start_time INTEGER NOT NULL,
        end_time INTEGER,
        duration INTEGER,
        category TEXT,
        tags TEXT,
        is_running INTEGER NOT NULL DEFAULT 0,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        synced_at INTEGER
      )
    ''');

    // Budget Tables
    await db.execute('''
      CREATE TABLE budgets (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        category_id TEXT,
        amount REAL NOT NULL,
        period TEXT NOT NULL,
        start_date INTEGER NOT NULL,
        end_date INTEGER,
        is_active INTEGER NOT NULL DEFAULT 1,
        description TEXT,
        alert_threshold REAL NOT NULL DEFAULT 0.8,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        synced_at INTEGER,
        FOREIGN KEY (category_id) REFERENCES categories (id)
      )
    ''');

    // Remove duplicate stores and products tables - using the ones above

    await db.execute('''
      CREATE TABLE stocktaking_sessions (
        id TEXT PRIMARY KEY,
        store_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        status TEXT NOT NULL DEFAULT 'draft',
        start_date INTEGER NOT NULL,
        end_date INTEGER,
        notes TEXT,
        total_products INTEGER NOT NULL DEFAULT 0,
        counted_products INTEGER NOT NULL DEFAULT 0,
        total_value REAL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        synced_at INTEGER,
        FOREIGN KEY (store_id) REFERENCES stores (id)
      )
    ''');

    // Remove duplicate athkar tables - using athkar_routines and athkar_progress above

    // Tools Builder Tables
    await db.execute('''
      CREATE TABLE formulas (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        type TEXT NOT NULL DEFAULT 'custom',
        expression TEXT NOT NULL,
        variables TEXT,
        result_unit TEXT,
        result_format TEXT,
        tags TEXT,
        is_public INTEGER NOT NULL DEFAULT 0,
        is_favorite INTEGER NOT NULL DEFAULT 0,
        usage_count INTEGER NOT NULL DEFAULT 0,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        synced_at INTEGER
      )
    ''');

    await db.execute('''
      CREATE TABLE calculations (
        id TEXT PRIMARY KEY,
        formula_id TEXT NOT NULL,
        formula_name TEXT NOT NULL,
        inputs TEXT NOT NULL,
        result TEXT,
        result_unit TEXT,
        notes TEXT,
        is_saved INTEGER NOT NULL DEFAULT 0,
        calculated_at INTEGER NOT NULL,
        synced_at INTEGER,
        FOREIGN KEY (formula_id) REFERENCES formulas (id)
      )
    ''');

    // Settings Table
    await db.execute('''
      CREATE TABLE settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < 2) {
      await _upgradeToVersion2(db);
    }
  }

  Future<void> _upgradeToVersion2(Database db) async {
    // Add missing columns to existing tables
    try {
      // Add missing columns to memos table
      await db.execute('ALTER TABLE memos ADD COLUMN type TEXT DEFAULT "text"');
      await db.execute('ALTER TABLE memos ADD COLUMN color INTEGER');
    } catch (e) {
      // Column might already exist, ignore error
    }

    try {
      // Add missing columns to accounts table
      await db.execute('ALTER TABLE accounts ADD COLUMN description TEXT');
    } catch (e) {
      // Column might already exist, ignore error
    }

    try {
      // Add missing columns to categories table
      await db.execute('ALTER TABLE categories ADD COLUMN description TEXT');
    } catch (e) {
      // Column might already exist, ignore error
    }

    try {
      // Add missing columns to stores table
      await db.execute('ALTER TABLE stores ADD COLUMN phone TEXT');
      await db.execute('ALTER TABLE stores ADD COLUMN email TEXT');
      await db.execute('ALTER TABLE stores ADD COLUMN description TEXT');
    } catch (e) {
      // Columns might already exist, ignore error
    }

    try {
      // Add missing columns to products table
      await db.execute('ALTER TABLE products ADD COLUMN description TEXT');
      await db.execute('ALTER TABLE products ADD COLUMN sku TEXT');
      await db.execute('ALTER TABLE products ADD COLUMN price REAL');
      await db.execute('ALTER TABLE products ADD COLUMN cost REAL');
      await db.execute(
        'ALTER TABLE products ADD COLUMN current_stock INTEGER NOT NULL DEFAULT 0',
      );
      await db.execute(
        'ALTER TABLE products ADD COLUMN min_stock INTEGER DEFAULT 0',
      );
      await db.execute('ALTER TABLE products ADD COLUMN max_stock INTEGER');
      await db.execute('ALTER TABLE products ADD COLUMN image_path TEXT');
      await db.execute('ALTER TABLE products ADD COLUMN location TEXT');
      await db.execute('ALTER TABLE products ADD COLUMN supplier TEXT');
    } catch (e) {
      // Columns might already exist, ignore error
    }

    try {
      // Add missing columns to time_entries table (rename from time_logs if needed)
      await db.execute('ALTER TABLE time_logs RENAME TO time_entries');
    } catch (e) {
      // Table might already be renamed or not exist
    }

    try {
      await db.execute('ALTER TABLE time_entries ADD COLUMN duration INTEGER');
      await db.execute(
        'ALTER TABLE time_entries ADD COLUMN is_running INTEGER NOT NULL DEFAULT 0',
      );
    } catch (e) {
      // Columns might already exist, ignore error
    }

    // Create new tables for enhanced Athkar functionality
    try {
      await db.execute('''
        CREATE TABLE IF NOT EXISTS routines (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          description TEXT,
          type TEXT NOT NULL DEFAULT 'daily',
          status TEXT NOT NULL DEFAULT 'active',
          target_count INTEGER NOT NULL DEFAULT 1,
          current_count INTEGER NOT NULL DEFAULT 0,
          start_date INTEGER NOT NULL,
          end_date INTEGER,
          tags TEXT,
          notes TEXT,
          is_notification_enabled INTEGER NOT NULL DEFAULT 0,
          notification_times TEXT,
          streak_count INTEGER NOT NULL DEFAULT 0,
          last_completed_at INTEGER,
          created_at INTEGER NOT NULL,
          updated_at INTEGER NOT NULL,
          synced_at INTEGER
        )
      ''');

      await db.execute('''
        CREATE TABLE IF NOT EXISTS multi_step_routines (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          description TEXT,
          category TEXT NOT NULL DEFAULT 'general',
          color INTEGER,
          is_sequential INTEGER NOT NULL DEFAULT 1,
          created_at INTEGER NOT NULL,
          updated_at INTEGER NOT NULL,
          synced_at INTEGER
        )
      ''');

      await db.execute('''
        CREATE TABLE IF NOT EXISTS athkar_steps (
          id TEXT PRIMARY KEY,
          routine_id TEXT NOT NULL,
          template_id TEXT,
          arabic_text TEXT NOT NULL,
          transliteration TEXT NOT NULL,
          translation TEXT NOT NULL,
          target_count INTEGER NOT NULL,
          current_count INTEGER NOT NULL DEFAULT 0,
          is_completed INTEGER NOT NULL DEFAULT 0,
          completed_at INTEGER,
          step_order INTEGER NOT NULL DEFAULT 0,
          created_at INTEGER NOT NULL,
          updated_at INTEGER NOT NULL,
          FOREIGN KEY (routine_id) REFERENCES multi_step_routines (id)
        )
      ''');
    } catch (e) {
      // Tables might already exist, ignore error
    }
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  Future<void> deleteDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, AppConstants.databaseName);
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }

  // ==================== GENERIC CRUD OPERATIONS ====================

  /// Generic insert method
  Future<int> insert(String table, Map<String, dynamic> data) async {
    final db = await database;
    try {
      return await db.insert(table, data);
    } catch (e) {
      print('Error inserting into $table: $e');
      rethrow;
    }
  }

  /// Generic select all method
  Future<List<Map<String, dynamic>>> selectAll(
    String table, {
    String? orderBy,
  }) async {
    final db = await database;
    try {
      return await db.query(table, orderBy: orderBy);
    } catch (e) {
      print('Error selecting from $table: $e');
      return [];
    }
  }

  /// Generic select method with where clause
  Future<List<Map<String, dynamic>>> select(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
    String? orderBy,
    int? limit,
  }) async {
    final db = await database;
    try {
      return await db.query(
        table,
        where: where,
        whereArgs: whereArgs,
        orderBy: orderBy,
        limit: limit,
      );
    } catch (e) {
      print('Error selecting from $table: $e');
      return [];
    }
  }

  /// Generic select by ID method
  Future<Map<String, dynamic>?> selectById(String table, String id) async {
    final db = await database;
    try {
      final results = await db.query(table, where: 'id = ?', whereArgs: [id]);
      return results.isNotEmpty ? results.first : null;
    } catch (e) {
      print('Error selecting from $table by id $id: $e');
      return null;
    }
  }

  /// Generic update method
  Future<int> update(String table, Map<String, dynamic> data, String id) async {
    final db = await database;
    try {
      data['updated_at'] = DateTime.now().millisecondsSinceEpoch;
      return await db.update(table, data, where: 'id = ?', whereArgs: [id]);
    } catch (e) {
      print('Error updating $table with id $id: $e');
      rethrow;
    }
  }

  /// Generic delete method
  Future<int> delete(String table, String id) async {
    final db = await database;
    try {
      return await db.delete(table, where: 'id = ?', whereArgs: [id]);
    } catch (e) {
      print('Error deleting from $table with id $id: $e');
      rethrow;
    }
  }

  /// Generic search method
  Future<List<Map<String, dynamic>>> search(
    String table,
    String column,
    String searchTerm, {
    String? orderBy,
  }) async {
    final db = await database;
    try {
      return await db.query(
        table,
        where: '$column LIKE ?',
        whereArgs: ['%$searchTerm%'],
        orderBy: orderBy,
      );
    } catch (e) {
      print('Error searching $table: $e');
      return [];
    }
  }

  /// Count records in table
  Future<int> count(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await database;
    try {
      final result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM $table${where != null ? ' WHERE $where' : ''}',
        whereArgs,
      );
      return result.first['count'] as int;
    } catch (e) {
      print('Error counting $table: $e');
      return 0;
    }
  }

  // ==================== SPECIFIC CRUD OPERATIONS ====================

  // Memo Suite Operations
  Future<String> insertMemo(Map<String, dynamic> memo) async {
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    final now = DateTime.now().millisecondsSinceEpoch;

    final data = {'id': id, 'created_at': now, 'updated_at': now, ...memo};

    await insert('memos', data);
    return id;
  }

  Future<List<Map<String, dynamic>>> getAllMemos() async {
    return await selectAll('memos', orderBy: 'created_at DESC');
  }

  Future<int> updateMemo(String id, Map<String, dynamic> memo) async {
    return await update('memos', memo, id);
  }

  Future<int> deleteMemo(String id) async {
    return await delete('memos', id);
  }

  // Money Flow Operations
  Future<String> insertAccount(Map<String, dynamic> account) async {
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    final now = DateTime.now().millisecondsSinceEpoch;

    final data = {'id': id, 'created_at': now, 'updated_at': now, ...account};

    await insert('accounts', data);
    return id;
  }

  Future<List<Map<String, dynamic>>> getAllAccounts() async {
    return await selectAll('accounts', orderBy: 'name ASC');
  }

  Future<String> insertTransaction(Map<String, dynamic> transaction) async {
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    final now = DateTime.now().millisecondsSinceEpoch;

    final data = {
      'id': id,
      'created_at': now,
      'updated_at': now,
      ...transaction,
    };

    await insert('transactions', data);
    return id;
  }

  Future<List<Map<String, dynamic>>> getAllTransactions() async {
    return await selectAll('transactions', orderBy: 'date DESC');
  }

  Future<String> insertCategory(Map<String, dynamic> category) async {
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    final now = DateTime.now().millisecondsSinceEpoch;

    final data = {'id': id, 'created_at': now, 'updated_at': now, ...category};

    await insert('categories', data);
    return id;
  }

  Future<List<Map<String, dynamic>>> getAllCategories() async {
    return await selectAll('categories', orderBy: 'name ASC');
  }

  // Tools Builder Operations
  Future<String> insertFormula(Map<String, dynamic> formula) async {
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    final now = DateTime.now().millisecondsSinceEpoch;

    final data = {'id': id, 'created_at': now, 'updated_at': now, ...formula};

    await insert('formulas', data);
    return id;
  }

  Future<List<Map<String, dynamic>>> getAllFormulas() async {
    return await selectAll('formulas', orderBy: 'name ASC');
  }

  // Athkar Pro Operations
  Future<String> insertRoutine(Map<String, dynamic> routine) async {
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    final now = DateTime.now().millisecondsSinceEpoch;

    final data = {'id': id, 'created_at': now, 'updated_at': now, ...routine};

    await insert('routines', data);
    return id;
  }

  Future<List<Map<String, dynamic>>> getAllRoutines() async {
    return await selectAll('routines', orderBy: 'name ASC');
  }

  // Multi-Step Routine Operations
  Future<String> insertMultiStepRoutine(Map<String, dynamic> routine) async {
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    final now = DateTime.now().millisecondsSinceEpoch;

    final data = {'id': id, 'created_at': now, 'updated_at': now, ...routine};

    await insert('multi_step_routines', data);
    return id;
  }

  Future<List<Map<String, dynamic>>> getAllMultiStepRoutines() async {
    return await selectAll('multi_step_routines', orderBy: 'name ASC');
  }

  Future<String> insertAthkarStep(Map<String, dynamic> step) async {
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    final now = DateTime.now().millisecondsSinceEpoch;

    final data = {'id': id, 'created_at': now, 'updated_at': now, ...step};

    await insert('athkar_steps', data);
    return id;
  }

  Future<List<Map<String, dynamic>>> getAthkarStepsByRoutine(
    String routineId,
  ) async {
    return await select(
      'athkar_steps',
      where: 'routine_id = ?',
      whereArgs: [routineId],
      orderBy: 'step_order ASC',
    );
  }

  // Stocktaking Pro Operations
  Future<String> insertStore(Map<String, dynamic> store) async {
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    final now = DateTime.now().millisecondsSinceEpoch;

    final data = {'id': id, 'created_at': now, 'updated_at': now, ...store};

    await insert('stores', data);
    return id;
  }

  Future<List<Map<String, dynamic>>> getAllStores() async {
    return await selectAll('stores', orderBy: 'name ASC');
  }

  Future<String> insertProduct(Map<String, dynamic> product) async {
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    final now = DateTime.now().millisecondsSinceEpoch;

    final data = {'id': id, 'created_at': now, 'updated_at': now, ...product};

    await insert('products', data);
    return id;
  }

  Future<List<Map<String, dynamic>>> getAllProducts() async {
    return await selectAll('products', orderBy: 'name ASC');
  }

  // TimeLog Operations
  Future<String> insertTimeEntry(Map<String, dynamic> timeEntry) async {
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    final now = DateTime.now().millisecondsSinceEpoch;

    final data = {'id': id, 'created_at': now, 'updated_at': now, ...timeEntry};

    await insert('time_entries', data);
    return id;
  }

  Future<List<Map<String, dynamic>>> getAllTimeEntries() async {
    return await selectAll('time_entries', orderBy: 'start_time DESC');
  }

  // ==================== SAMPLE DATA INITIALIZATION ====================

  /// Initialize database with sample data for testing
  Future<void> initializeWithSampleData() async {
    final db = await database;

    // Check if data already exists
    final memoCount = await db.rawQuery('SELECT COUNT(*) as count FROM memos');
    if ((memoCount.first['count'] as int) > 0) return;

    // Insert sample data
    await _insertSampleData(db);
  }

  Future<void> _insertSampleData(Database db) async {
    final now = DateTime.now().millisecondsSinceEpoch;

    // Sample accounts
    await db.insert('accounts', {
      'id': 'acc_1',
      'name': 'Main Checking',
      'type': 'checking',
      'balance': 2500.0,
      'currency': 'USD',
      'is_active': 1,
      'created_at': now,
      'updated_at': now,
    });

    await db.insert('accounts', {
      'id': 'acc_2',
      'name': 'Savings Account',
      'type': 'savings',
      'balance': 10000.0,
      'currency': 'USD',
      'is_active': 1,
      'created_at': now,
      'updated_at': now,
    });

    // Sample categories
    await db.insert('categories', {
      'id': 'cat_1',
      'name': 'Food & Dining',
      'type': 'expense',
      'color': 'red',
      'icon': 'restaurant',
      'created_at': now,
      'updated_at': now,
    });

    await db.insert('categories', {
      'id': 'cat_2',
      'name': 'Transportation',
      'type': 'expense',
      'color': 'blue',
      'icon': 'directions_car',
      'created_at': now,
      'updated_at': now,
    });

    await db.insert('categories', {
      'id': 'cat_3',
      'name': 'Salary',
      'type': 'income',
      'color': 'green',
      'icon': 'attach_money',
      'created_at': now,
      'updated_at': now,
    });

    // Sample transactions
    await db.insert('transactions', {
      'id': 'trans_1',
      'account_id': 'acc_1',
      'category_id': 'cat_1',
      'amount': -45.50,
      'description': 'Lunch at restaurant',
      'date': now - (24 * 60 * 60 * 1000), // Yesterday
      'type': 'expense',
      'created_at': now,
      'updated_at': now,
    });

    await db.insert('transactions', {
      'id': 'trans_2',
      'account_id': 'acc_1',
      'category_id': 'cat_3',
      'amount': 3000.0,
      'description': 'Monthly salary',
      'date': now - (7 * 24 * 60 * 60 * 1000), // Week ago
      'type': 'income',
      'created_at': now,
      'updated_at': now,
    });

    // Sample stores
    await db.insert('stores', {
      'id': 'store_1',
      'name': 'Main Warehouse',
      'address': '123 Business District',
      'manager': 'John Smith',
      'created_at': now,
      'updated_at': now,
    });

    await db.insert('stores', {
      'id': 'store_2',
      'name': 'Retail Outlet',
      'address': '456 Shopping Center',
      'manager': 'Jane Doe',
      'created_at': now,
      'updated_at': now,
    });

    // Sample products
    await db.insert('products', {
      'id': 'prod_1',
      'store_id': 'store_1',
      'name': 'Wireless Headphones',
      'barcode': '*************',
      'category': 'Electronics',
      'unit_price': 99.99,
      'quantity': 25,
      'min_quantity': 5,
      'created_at': now,
      'updated_at': now,
    });

    await db.insert('products', {
      'id': 'prod_2',
      'store_id': 'store_1',
      'name': 'Smartphone Case',
      'barcode': '2345678901234',
      'category': 'Accessories',
      'unit_price': 24.99,
      'quantity': 50,
      'min_quantity': 10,
      'created_at': now,
      'updated_at': now,
    });

    // Sample memos
    await db.insert('memos', {
      'id': 'memo_1',
      'title': 'Meeting Notes',
      'content': 'Discussed project timeline and deliverables for Q1.',
      'tags': 'meeting,project,timeline',
      'created_at': now,
      'updated_at': now,
    });

    await db.insert('memos', {
      'id': 'memo_2',
      'title': 'Shopping List',
      'content': 'Milk, Bread, Eggs, Apples, Chicken',
      'tags': 'shopping,groceries',
      'created_at': now,
      'updated_at': now,
    });

    // Sample routines
    await db.insert('routines', {
      'id': 'routine_1',
      'name': 'Morning Routine',
      'description': 'Daily morning routine',
      'type': 'daily',
      'status': 'active',
      'target_count': 1,
      'current_count': 0,
      'start_date': now,
      'tags': 'morning,daily',
      'is_notification_enabled': 1,
      'notification_times': '07:00',
      'streak_count': 0,
      'created_at': now,
      'updated_at': now,
    });

    await db.insert('routines', {
      'id': 'routine_2',
      'name': 'Evening Routine',
      'description': 'Daily evening routine',
      'type': 'daily',
      'status': 'active',
      'target_count': 1,
      'current_count': 0,
      'start_date': now,
      'tags': 'evening,daily',
      'is_notification_enabled': 1,
      'notification_times': '19:00',
      'streak_count': 0,
      'created_at': now,
      'updated_at': now,
    });

    // Sample multi-step routines
    await db.insert('multi_step_routines', {
      'id': 'multi_routine_1',
      'name': 'Morning Athkar',
      'description': 'Essential morning remembrance routine',
      'category': 'morning',
      'is_sequential': 1,
      'created_at': now,
      'updated_at': now,
    });

    // Sample athkar steps
    await db.insert('athkar_steps', {
      'id': 'step_1',
      'routine_id': 'multi_routine_1',
      'template_id': 'morning_1',
      'arabic_text': 'أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ',
      'transliteration': 'A\'udhu billahi min ash-shaytani\'r-rajim',
      'translation': 'I seek refuge in Allah from Satan, the accursed',
      'target_count': 3,
      'current_count': 0,
      'is_completed': 0,
      'step_order': 0,
      'created_at': now,
      'updated_at': now,
    });

    await db.insert('athkar_steps', {
      'id': 'step_2',
      'routine_id': 'multi_routine_1',
      'template_id': 'morning_2',
      'arabic_text': 'بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ',
      'transliteration': 'Bismillahi\'r-rahmani\'r-rahim',
      'translation':
          'In the name of Allah, the Most Gracious, the Most Merciful',
      'target_count': 1,
      'current_count': 0,
      'is_completed': 0,
      'step_order': 1,
      'created_at': now,
      'updated_at': now,
    });

    // Sample formulas
    await db.insert('formulas', {
      'id': 'formula_1',
      'name': 'Circle Area',
      'description': 'Calculate the area of a circle',
      'expression': 'π * r²',
      'variables': '{"r": {"name": "radius", "unit": "cm", "default": 1}}',
      'result_unit': 'cm²',
      'type': 'geometry',
      'created_at': now,
      'updated_at': now,
    });

    await db.insert('formulas', {
      'id': 'formula_2',
      'name': 'BMI Calculator',
      'description': 'Calculate Body Mass Index',
      'expression': 'weight / (height²)',
      'variables':
          '{"weight": {"name": "weight", "unit": "kg", "default": 70}, "height": {"name": "height", "unit": "m", "default": 1.75}}',
      'result_unit': 'kg/m²',
      'type': 'health',
      'created_at': now,
      'updated_at': now,
    });

    // Sample time entries
    await db.insert('time_entries', {
      'id': 'log_1',
      'title': 'Project Development',
      'description': 'Working on ShadowSuite app features',
      'start_time': now - (2 * 60 * 60 * 1000), // 2 hours ago
      'end_time': now,
      'duration': 2 * 60 * 60 * 1000, // 2 hours in milliseconds
      'category': 'Work',
      'tags': 'development,flutter,project',
      'is_running': 0,
      'created_at': now,
      'updated_at': now,
    });

    await db.insert('time_entries', {
      'id': 'log_2',
      'title': 'Reading',
      'description': 'Reading technical documentation',
      'start_time': now - (24 * 60 * 60 * 1000), // Yesterday
      'end_time': now - (23 * 60 * 60 * 1000), // 1 hour session
      'duration': 60 * 60 * 1000, // 1 hour in milliseconds
      'category': 'Learning',
      'tags': 'reading,documentation,learning',
      'is_running': 0,
      'created_at': now,
      'updated_at': now,
    });
  }
}
