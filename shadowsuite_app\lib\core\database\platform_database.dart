import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'database_helper.dart';

// Abstract interface for database operations
abstract class PlatformDatabase {
  Future<void> initialize();
  Future<void> initializeWithSampleData();

  // Generic CRUD operations
  Future<int> insert(String table, Map<String, dynamic> data);
  Future<List<Map<String, dynamic>>> select(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  });
  Future<int> update(
    String table,
    Map<String, dynamic> data, {
    String? where,
    List<dynamic>? whereArgs,
  });
  Future<int> delete(String table, {String? where, List<dynamic>? whereArgs});
  Future<List<Map<String, dynamic>>> rawQuery(
    String sql, [
    List<dynamic>? arguments,
  ]);
  Future<int> rawInsert(String sql, [List<dynamic>? arguments]);
  Future<int> rawUpdate(String sql, [List<dynamic>? arguments]);
  Future<int> rawDelete(String sql, [List<dynamic>? arguments]);

  // Utility methods
  Future<int> count(String table, {String? where, List<dynamic>? whereArgs});
  Future<List<Map<String, dynamic>>> search(
    String table,
    String searchColumn,
    String searchTerm,
  );
  Future<void> close();
}

// Factory to create appropriate database instance
class DatabaseFactory {
  static PlatformDatabase? _instance;

  static PlatformDatabase getInstance() {
    _instance ??= kIsWeb ? WebDatabase() : MobileDatabase();
    return _instance!;
  }
}

// Web implementation using SharedPreferences
class WebDatabase implements PlatformDatabase {
  late SharedPreferences _prefs;
  static const String _dataPrefix = 'db_';
  static const String _counterPrefix = 'counter_';

  @override
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }

  @override
  Future<void> initializeWithSampleData() async {
    await initialize();

    // Check if sample data already exists
    final hasData =
        _prefs.getBool('${_dataPrefix}sample_data_initialized') ?? false;
    if (hasData) return;

    // Initialize sample data for all tables
    await _initializeSampleMemos();
    await _initializeSampleAccounts();
    await _initializeSampleCategories();
    await _initializeSampleTransactions();
    await _initializeSampleFormulas();
    await _initializeSampleRoutines();
    await _initializeSampleStores();
    await _initializeSampleProducts();
    await _initializeSampleTimeLogs();
    await _initializeSampleMultiStepRoutines();
    await _initializeSampleAthkarSteps();

    // Mark sample data as initialized
    await _prefs.setBool('${_dataPrefix}sample_data_initialized', true);
  }

  @override
  Future<int> insert(String table, Map<String, dynamic> data) async {
    final id = await _getNextId(table);
    data['id'] = id.toString();

    final tableData = await _getTableData(table);
    tableData.add(data);
    await _saveTableData(table, tableData);

    return id;
  }

  @override
  Future<List<Map<String, dynamic>>> select(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final tableData = await _getTableData(table);

    if (where == null) return tableData;

    // Simple where clause parsing for web
    return tableData
        .where((row) => _matchesWhere(row, where, whereArgs))
        .toList();
  }

  @override
  Future<int> update(
    String table,
    Map<String, dynamic> data, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final tableData = await _getTableData(table);
    int updatedCount = 0;

    for (int i = 0; i < tableData.length; i++) {
      if (where == null || _matchesWhere(tableData[i], where, whereArgs)) {
        tableData[i] = {...tableData[i], ...data};
        updatedCount++;
      }
    }

    await _saveTableData(table, tableData);
    return updatedCount;
  }

  @override
  Future<int> delete(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final tableData = await _getTableData(table);
    final originalLength = tableData.length;

    if (where == null) {
      tableData.clear();
    } else {
      tableData.removeWhere((row) => _matchesWhere(row, where, whereArgs));
    }

    await _saveTableData(table, tableData);
    return originalLength - tableData.length;
  }

  @override
  Future<List<Map<String, dynamic>>> rawQuery(
    String sql, [
    List<dynamic>? arguments,
  ]) async {
    // Simple SQL parsing for common queries
    final sqlLower = sql.toLowerCase().trim();

    if (sqlLower.startsWith('select')) {
      // Extract table name from simple SELECT queries
      final tableMatch = RegExp(
        r'from\s+(\w+)',
        caseSensitive: false,
      ).firstMatch(sql);
      if (tableMatch != null) {
        final table = tableMatch.group(1)!;
        return await select(table);
      }
    }

    return [];
  }

  @override
  Future<int> rawInsert(String sql, [List<dynamic>? arguments]) async {
    // For web, we'll use the insert method with parsed data
    return 0; // Placeholder
  }

  @override
  Future<int> rawUpdate(String sql, [List<dynamic>? arguments]) async {
    return 0; // Placeholder
  }

  @override
  Future<int> rawDelete(String sql, [List<dynamic>? arguments]) async {
    return 0; // Placeholder
  }

  @override
  Future<int> count(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final results = await select(table, where: where, whereArgs: whereArgs);
    return results.length;
  }

  @override
  Future<List<Map<String, dynamic>>> search(
    String table,
    String searchColumn,
    String searchTerm,
  ) async {
    final tableData = await _getTableData(table);
    return tableData.where((row) {
      final value = row[searchColumn]?.toString().toLowerCase() ?? '';
      return value.contains(searchTerm.toLowerCase());
    }).toList();
  }

  @override
  Future<void> close() async {
    // No need to close SharedPreferences
  }

  // Helper methods
  Future<List<Map<String, dynamic>>> _getTableData(String table) async {
    final dataString = _prefs.getString('${_dataPrefix}$table');
    if (dataString == null) return [];

    try {
      final List<dynamic> dataList = jsonDecode(dataString);
      return dataList.cast<Map<String, dynamic>>();
    } catch (e) {
      return [];
    }
  }

  Future<void> _saveTableData(
    String table,
    List<Map<String, dynamic>> data,
  ) async {
    final dataString = jsonEncode(data);
    await _prefs.setString('${_dataPrefix}$table', dataString);
  }

  Future<int> _getNextId(String table) async {
    final currentId = _prefs.getInt('${_counterPrefix}$table') ?? 0;
    final nextId = currentId + 1;
    await _prefs.setInt('${_counterPrefix}$table', nextId);
    return nextId;
  }

  bool _matchesWhere(
    Map<String, dynamic> row,
    String where,
    List<dynamic>? whereArgs,
  ) {
    // Simple where clause matching
    if (where.contains('=') && whereArgs != null && whereArgs.isNotEmpty) {
      final parts = where.split('=');
      if (parts.length == 2) {
        final column = parts[0].trim();
        final value = whereArgs[0];
        return row[column]?.toString() == value.toString();
      }
    }
    return true;
  }

  // Sample data initialization methods
  Future<void> _initializeSampleMemos() async {
    final sampleMemos = [
      {
        'id': '1',
        'title': 'Welcome to ShadowSuite',
        'content': 'This is your first memo in the ShadowSuite app!',
        'audioPath': null,
        'transcription': null,
        'tags': 'welcome,first',
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      },
      {
        'id': '2',
        'title': 'Meeting Notes',
        'content': 'Important points from today\'s meeting',
        'audioPath': null,
        'transcription': null,
        'tags': 'meeting,work',
        'createdAt': DateTime.now()
            .subtract(const Duration(days: 1))
            .toIso8601String(),
        'updatedAt': DateTime.now()
            .subtract(const Duration(days: 1))
            .toIso8601String(),
      },
    ];

    await _saveTableData('memos', sampleMemos);
    await _prefs.setInt('${_counterPrefix}memos', 2);
  }

  Future<void> _initializeSampleAccounts() async {
    final sampleAccounts = [
      {
        'id': '1',
        'name': 'Main Checking',
        'type': 'checking',
        'balance': 2500.0,
        'currency': 'USD',
        'isActive': 1,
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      },
      {
        'id': '2',
        'name': 'Savings Account',
        'type': 'savings',
        'balance': 10000.0,
        'currency': 'USD',
        'isActive': 1,
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      },
    ];

    await _saveTableData('accounts', sampleAccounts);
    await _prefs.setInt('${_counterPrefix}accounts', 2);
  }

  Future<void> _initializeSampleCategories() async {
    final sampleCategories = [
      {
        'id': '1',
        'name': 'Food & Dining',
        'type': 'expense',
        'color': 'orange',
        'icon': 'restaurant',
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      },
      {
        'id': '2',
        'name': 'Salary',
        'type': 'income',
        'color': 'green',
        'icon': 'work',
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      },
    ];

    await _saveTableData('categories', sampleCategories);
    await _prefs.setInt('${_counterPrefix}categories', 2);
  }

  Future<void> _initializeSampleTransactions() async {
    final sampleTransactions = [
      {
        'id': '1',
        'accountId': '1',
        'categoryId': '1',
        'amount': -25.50,
        'description': 'Lunch at restaurant',
        'date': DateTime.now().toIso8601String(),
        'type': 'expense',
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      },
      {
        'id': '2',
        'accountId': '1',
        'categoryId': '2',
        'amount': 3000.0,
        'description': 'Monthly salary',
        'date': DateTime.now()
            .subtract(const Duration(days: 5))
            .toIso8601String(),
        'type': 'income',
        'createdAt': DateTime.now()
            .subtract(const Duration(days: 5))
            .toIso8601String(),
        'updatedAt': DateTime.now()
            .subtract(const Duration(days: 5))
            .toIso8601String(),
      },
    ];

    await _saveTableData('transactions', sampleTransactions);
    await _prefs.setInt('${_counterPrefix}transactions', 2);
  }

  Future<void> _initializeSampleFormulas() async {
    await _saveTableData('formulas', []);
    await _prefs.setInt('${_counterPrefix}formulas', 0);
  }

  Future<void> _initializeSampleRoutines() async {
    await _saveTableData('routines', []);
    await _prefs.setInt('${_counterPrefix}routines', 0);
  }

  Future<void> _initializeSampleStores() async {
    await _saveTableData('stores', []);
    await _prefs.setInt('${_counterPrefix}stores', 0);
  }

  Future<void> _initializeSampleProducts() async {
    await _saveTableData('products', []);
    await _prefs.setInt('${_counterPrefix}products', 0);
  }

  Future<void> _initializeSampleTimeLogs() async {
    final sampleTimeLogs = [
      {
        'id': '1',
        'title': 'Working on ShadowSuite',
        'description': 'Developing the Flutter application',
        'project_id': null,
        'category_id': null,
        'start_time': DateTime.now()
            .subtract(const Duration(hours: 2))
            .millisecondsSinceEpoch,
        'end_time': DateTime.now()
            .subtract(const Duration(hours: 1))
            .millisecondsSinceEpoch,
        'duration': const Duration(hours: 1).inMilliseconds,
        'status': 'completed',
        'tags': 'development,flutter',
        'created_at': DateTime.now()
            .subtract(const Duration(hours: 2))
            .millisecondsSinceEpoch,
        'updated_at': DateTime.now()
            .subtract(const Duration(hours: 1))
            .millisecondsSinceEpoch,
        'synced_at': null,
      },
      {
        'id': '2',
        'title': 'Code Review',
        'description': 'Reviewing pull requests',
        'project_id': null,
        'category_id': null,
        'start_time': DateTime.now()
            .subtract(const Duration(minutes: 30))
            .millisecondsSinceEpoch,
        'end_time': DateTime.now().millisecondsSinceEpoch,
        'duration': const Duration(minutes: 30).inMilliseconds,
        'status': 'completed',
        'tags': 'review,code',
        'created_at': DateTime.now()
            .subtract(const Duration(minutes: 30))
            .millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
        'synced_at': null,
      },
    ];

    await _saveTableData('time_entries', sampleTimeLogs);
    await _prefs.setInt('${_counterPrefix}time_entries', 2);
  }

  Future<void> _initializeSampleMultiStepRoutines() async {
    final sampleRoutines = [
      {
        'id': '1',
        'name': 'Morning Athkar',
        'description': 'Essential morning remembrance routine',
        'category': 'morning',
        'color': null,
        'is_sequential': 1,
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
        'synced_at': null,
      },
      {
        'id': '2',
        'name': 'Evening Athkar',
        'description': 'Essential evening remembrance routine',
        'category': 'evening',
        'color': null,
        'is_sequential': 1,
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
        'synced_at': null,
      },
    ];

    await _saveTableData('multi_step_routines', sampleRoutines);
    await _prefs.setInt('${_counterPrefix}multi_step_routines', 2);
  }

  Future<void> _initializeSampleAthkarSteps() async {
    final sampleSteps = [
      {
        'id': '1',
        'routine_id': '1',
        'template_id': 'morning_1',
        'arabic_text': 'أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ',
        'transliteration': 'A\'udhu billahi min ash-shaytani\'r-rajim',
        'translation': 'I seek refuge in Allah from Satan, the accursed',
        'target_count': 3,
        'current_count': 0,
        'is_completed': 0,
        'completed_at': null,
        'step_order': 0,
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      },
      {
        'id': '2',
        'routine_id': '1',
        'template_id': 'morning_2',
        'arabic_text': 'بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ',
        'transliteration': 'Bismillahi\'r-rahmani\'r-rahim',
        'translation':
            'In the name of Allah, the Most Gracious, the Most Merciful',
        'target_count': 1,
        'current_count': 0,
        'is_completed': 0,
        'completed_at': null,
        'step_order': 1,
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      },
    ];

    await _saveTableData('athkar_steps', sampleSteps);
    await _prefs.setInt('${_counterPrefix}athkar_steps', 2);
  }
}

// Mobile implementation using SQLite
class MobileDatabase implements PlatformDatabase {
  late DatabaseHelper _databaseHelper;

  @override
  Future<void> initialize() async {
    _databaseHelper = DatabaseHelper();
    await _databaseHelper.database;
  }

  @override
  Future<void> initializeWithSampleData() async {
    await initialize();
    await _databaseHelper.initializeWithSampleData();
  }

  @override
  Future<int> insert(String table, Map<String, dynamic> data) async {
    return await _databaseHelper.insert(table, data);
  }

  @override
  Future<List<Map<String, dynamic>>> select(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await _databaseHelper.database;
    return await db.query(table, where: where, whereArgs: whereArgs);
  }

  @override
  Future<int> update(
    String table,
    Map<String, dynamic> data, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await _databaseHelper.database;
    return await db.update(table, data, where: where, whereArgs: whereArgs);
  }

  @override
  Future<int> delete(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await _databaseHelper.database;
    return await db.delete(table, where: where, whereArgs: whereArgs);
  }

  @override
  Future<List<Map<String, dynamic>>> rawQuery(
    String sql, [
    List<dynamic>? arguments,
  ]) async {
    final db = await _databaseHelper.database;
    return await db.rawQuery(sql, arguments);
  }

  @override
  Future<int> rawInsert(String sql, [List<dynamic>? arguments]) async {
    final db = await _databaseHelper.database;
    return await db.rawInsert(sql, arguments);
  }

  @override
  Future<int> rawUpdate(String sql, [List<dynamic>? arguments]) async {
    final db = await _databaseHelper.database;
    return await db.rawUpdate(sql, arguments);
  }

  @override
  Future<int> rawDelete(String sql, [List<dynamic>? arguments]) async {
    final db = await _databaseHelper.database;
    return await db.rawDelete(sql, arguments);
  }

  @override
  Future<int> count(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    return await _databaseHelper.count(
      table,
      where: where,
      whereArgs: whereArgs,
    );
  }

  @override
  Future<List<Map<String, dynamic>>> search(
    String table,
    String searchColumn,
    String searchTerm,
  ) async {
    return await _databaseHelper.search(table, searchColumn, searchTerm);
  }

  @override
  Future<void> close() async {
    await _databaseHelper.close();
  }
}
