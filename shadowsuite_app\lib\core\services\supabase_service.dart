import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../constants/app_constants.dart';

class SupabaseService {
  static final SupabaseService _instance = SupabaseService._internal();
  SupabaseService._internal();
  factory SupabaseService() => _instance;

  late SupabaseClient _client;
  bool _isInitialized = false;

  SupabaseClient get client => _client;
  bool get isInitialized => _isInitialized;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await Supabase.initialize(
        url: AppConstants.supabaseUrl,
        anonKey: AppConstants.supabaseAnonKey,
        debug: kDebugMode,
      );
      
      _client = Supabase.instance.client;
      _isInitialized = true;

      debugPrint('✅ Supabase initialized successfully');
      
      // Create database schema if needed
      await _createDatabaseSchema();
      
      // Initialize sample data
      await _initializeSampleData();
      
    } catch (e) {
      debugPrint('❌ Failed to initialize Supabase: $e');
      // Continue without Supabase for offline functionality
    }
  }

  Future<bool> isOnline() async {
    if (!_isInitialized) return false;
    
    try {
      // Simple connectivity test
      await _client.from('user_profiles').select('id').limit(1);
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<void> _createDatabaseSchema() async {
    if (!_isInitialized) return;

    try {
      debugPrint('🔧 Creating Supabase database schema...');
      
      // Note: In a real Supabase setup, you would create these tables through the Supabase dashboard
      // or using SQL migrations. This is a simplified approach for demonstration.
      
      final tables = [
        'user_profiles',
        'memos',
        'routines',
        'multi_step_routines',
        'athkar_steps',
        'athkar_progress',
        'accounts',
        'categories',
        'transactions',
        'custom_tools',
        'formulas',
        'stores',
        'products',
        'time_entries',
        'files',
        'folders',
        'network_devices',
        'network_connections',
        'settings',
      ];

      // Check if tables exist (this is a simplified check)
      for (final table in tables) {
        try {
          await _client.from(table).select('*').limit(1);
          debugPrint('✅ Table $table exists');
        } catch (e) {
          debugPrint('⚠️ Table $table may not exist: $e');
        }
      }

      debugPrint('✅ Database schema check completed');
    } catch (e) {
      debugPrint('❌ Error creating database schema: $e');
    }
  }

  Future<void> _initializeSampleData() async {
    if (!_isInitialized) return;

    try {
      debugPrint('📝 Initializing sample data...');
      
      // Check if we already have data
      final existingProfiles = await _client
          .from('user_profiles')
          .select('id')
          .limit(1);
      
      if (existingProfiles.isNotEmpty) {
        debugPrint('✅ Sample data already exists');
        return;
      }

      // Create sample user profile
      await _client.from('user_profiles').insert({
        'id': 'sample-user-1',
        'name': 'Sample User',
        'email': '<EMAIL>',
        'preferences': '{}',
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      });

      // Create sample athkar routines
      await _createSampleAthkarRoutines();
      
      // Create sample memo categories
      await _createSampleMemoData();
      
      // Create sample financial data
      await _createSampleFinancialData();

      debugPrint('✅ Sample data initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing sample data: $e');
    }
  }

  Future<void> _createSampleAthkarRoutines() async {
    try {
      // Sample multi-step routine
      final routineId = 'morning-athkar-routine';
      
      await _client.from('multi_step_routines').insert({
        'id': routineId,
        'name': 'Morning Athkar',
        'description': 'Essential morning remembrance of Allah',
        'category': 'morning',
        'is_sequential': true,
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      });

      // Sample athkar steps
      final steps = [
        {
          'id': 'step-1',
          'routine_id': routineId,
          'template_id': 'subhan-allah',
          'arabic_text': 'سُبْحَانَ اللَّهِ',
          'transliteration': 'Subhan Allah',
          'translation': 'Glory be to Allah',
          'target_count': 33,
          'current_count': 0,
          'is_completed': false,
          'step_order': 1,
        },
        {
          'id': 'step-2',
          'routine_id': routineId,
          'template_id': 'alhamdulillah',
          'arabic_text': 'الْحَمْدُ لِلَّهِ',
          'transliteration': 'Alhamdulillah',
          'translation': 'All praise is due to Allah',
          'target_count': 33,
          'current_count': 0,
          'is_completed': false,
          'step_order': 2,
        },
        {
          'id': 'step-3',
          'routine_id': routineId,
          'template_id': 'allahu-akbar',
          'arabic_text': 'اللَّهُ أَكْبَرُ',
          'transliteration': 'Allahu Akbar',
          'translation': 'Allah is the Greatest',
          'target_count': 34,
          'current_count': 0,
          'is_completed': false,
          'step_order': 3,
        },
      ];

      for (final step in steps) {
        await _client.from('athkar_steps').insert(step);
      }

      debugPrint('✅ Sample athkar routines created');
    } catch (e) {
      debugPrint('❌ Error creating sample athkar routines: $e');
    }
  }

  Future<void> _createSampleMemoData() async {
    try {
      // Sample memo
      await _client.from('memos').insert({
        'id': 'sample-memo-1',
        'title': 'Welcome to ShadowSuite',
        'content': 'This is your first memo in ShadowSuite. You can create voice notes, text memos, and organize them with colors and tags.',
        'type': 'text',
        'color': 'blue',
        'tags': 'welcome,getting-started',
        'is_pinned': true,
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      });

      debugPrint('✅ Sample memo data created');
    } catch (e) {
      debugPrint('❌ Error creating sample memo data: $e');
    }
  }

  Future<void> _createSampleFinancialData() async {
    try {
      // Sample accounts
      await _client.from('accounts').insert([
        {
          'id': 'cash-account',
          'name': 'Cash',
          'type': 'cash',
          'balance': 1000.0,
          'currency': 'USD',
          'description': 'Cash on hand',
          'created_at': DateTime.now().millisecondsSinceEpoch,
          'updated_at': DateTime.now().millisecondsSinceEpoch,
        },
        {
          'id': 'bank-account',
          'name': 'Bank Account',
          'type': 'bank',
          'balance': 5000.0,
          'currency': 'USD',
          'description': 'Primary bank account',
          'created_at': DateTime.now().millisecondsSinceEpoch,
          'updated_at': DateTime.now().millisecondsSinceEpoch,
        },
      ]);

      // Sample categories
      await _client.from('categories').insert([
        {
          'id': 'food-category',
          'name': 'Food & Dining',
          'type': 'expense',
          'color': 'orange',
          'icon': 'restaurant',
          'description': 'Meals and dining expenses',
          'created_at': DateTime.now().millisecondsSinceEpoch,
          'updated_at': DateTime.now().millisecondsSinceEpoch,
        },
        {
          'id': 'salary-category',
          'name': 'Salary',
          'type': 'income',
          'color': 'green',
          'icon': 'work',
          'description': 'Monthly salary income',
          'created_at': DateTime.now().millisecondsSinceEpoch,
          'updated_at': DateTime.now().millisecondsSinceEpoch,
        },
      ]);

      // Sample transaction
      await _client.from('transactions').insert({
        'id': 'sample-transaction-1',
        'account_id': 'cash-account',
        'category_id': 'food-category',
        'amount': -25.50,
        'description': 'Lunch at restaurant',
        'date': DateTime.now().millisecondsSinceEpoch,
        'type': 'expense',
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      });

      debugPrint('✅ Sample financial data created');
    } catch (e) {
      debugPrint('❌ Error creating sample financial data: $e');
    }
  }

  Future<Map<String, dynamic>?> getUserProfile(String userId) async {
    if (!_isInitialized) return null;

    try {
      final response = await _client
          .from('user_profiles')
          .select()
          .eq('id', userId)
          .single();
      return response;
    } catch (e) {
      debugPrint('Error fetching user profile: $e');
      return null;
    }
  }

  Future<void> updateUserProfile(String userId, Map<String, dynamic> data) async {
    if (!_isInitialized) return;

    try {
      await _client
          .from('user_profiles')
          .update({
            ...data,
            'updated_at': DateTime.now().millisecondsSinceEpoch,
          })
          .eq('id', userId);
    } catch (e) {
      debugPrint('Error updating user profile: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getMultiStepRoutines() async {
    if (!_isInitialized) return [];

    try {
      final response = await _client
          .from('multi_step_routines')
          .select('*, athkar_steps(*)')
          .order('created_at', ascending: false);
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error fetching multi-step routines: $e');
      return [];
    }
  }

  Future<void> createMultiStepRoutine(Map<String, dynamic> routine, List<Map<String, dynamic>> steps) async {
    if (!_isInitialized) return;

    try {
      // Insert routine
      await _client.from('multi_step_routines').insert(routine);
      
      // Insert steps
      for (final step in steps) {
        await _client.from('athkar_steps').insert(step);
      }
    } catch (e) {
      debugPrint('Error creating multi-step routine: $e');
    }
  }

  void dispose() {
    // Cleanup if needed
  }
}
