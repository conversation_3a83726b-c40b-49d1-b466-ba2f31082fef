import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/athkar.dart';
import '../services/multi_step_routine_service.dart';

// Service provider
final multiStepRoutineServiceProvider = Provider<MultiStepRoutineService>((ref) {
  return MultiStepRoutineService();
});

// State class for multi-step routines
class MultiStepRoutineState {
  final List<MultiStepRoutine> routines;
  final bool isLoading;
  final String? error;
  final MultiStepRoutine? selectedRoutine;
  final Map<String, Map<String, dynamic>> routineStats;

  const MultiStepRoutineState({
    this.routines = const [],
    this.isLoading = false,
    this.error,
    this.selectedRoutine,
    this.routineStats = const {},
  });

  MultiStepRoutineState copyWith({
    List<MultiStepRoutine>? routines,
    bool? isLoading,
    String? error,
    MultiStepRoutine? selectedRoutine,
    Map<String, Map<String, dynamic>>? routineStats,
  }) {
    return MultiStepRoutineState(
      routines: routines ?? this.routines,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      selectedRoutine: selectedRoutine ?? this.selectedRoutine,
      routineStats: routineStats ?? this.routineStats,
    );
  }
}

// StateNotifier for managing multi-step routines
class MultiStepRoutineNotifier extends StateNotifier<MultiStepRoutineState> {
  final MultiStepRoutineService _service;

  MultiStepRoutineNotifier(this._service) : super(const MultiStepRoutineState()) {
    _initialize();
  }

  Future<void> _initialize() async {
    await _service.initialize();
    await refresh();
  }

  // ==================== CRUD OPERATIONS ====================

  /// Refresh the list of routines
  Future<void> refresh() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final routines = await _service.getAllRoutines();
      state = state.copyWith(
        routines: routines,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load routines: $e',
      );
      if (kDebugMode) print('Error refreshing routines: $e');
    }
  }

  /// Add a new routine
  Future<void> addRoutine(MultiStepRoutine routine) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      await _service.createRoutine(routine);
      await refresh();
      
      if (kDebugMode) print('Routine added successfully: ${routine.name}');
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to add routine: $e',
      );
      if (kDebugMode) print('Error adding routine: $e');
      rethrow;
    }
  }

  /// Update an existing routine
  Future<void> updateRoutine(MultiStepRoutine routine) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      await _service.updateRoutine(routine);
      await refresh();
      
      // Update selected routine if it's the same one
      if (state.selectedRoutine?.id == routine.id) {
        state = state.copyWith(selectedRoutine: routine);
      }
      
      if (kDebugMode) print('Routine updated successfully: ${routine.name}');
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update routine: $e',
      );
      if (kDebugMode) print('Error updating routine: $e');
      rethrow;
    }
  }

  /// Delete a routine
  Future<void> deleteRoutine(String routineId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      await _service.deleteRoutine(routineId);
      await refresh();
      
      // Clear selected routine if it was deleted
      if (state.selectedRoutine?.id == routineId) {
        state = state.copyWith(selectedRoutine: null);
      }
      
      if (kDebugMode) print('Routine deleted successfully');
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to delete routine: $e',
      );
      if (kDebugMode) print('Error deleting routine: $e');
      rethrow;
    }
  }

  // ==================== SELECTION OPERATIONS ====================

  /// Select a routine for detailed view/execution
  Future<void> selectRoutine(String routineId) async {
    try {
      final routine = await _service.getRoutineById(routineId);
      if (routine != null) {
        state = state.copyWith(selectedRoutine: routine);
        await _loadRoutineStats(routineId);
      }
    } catch (e) {
      state = state.copyWith(error: 'Failed to select routine: $e');
      if (kDebugMode) print('Error selecting routine: $e');
    }
  }

  /// Clear the selected routine
  void clearSelection() {
    state = state.copyWith(selectedRoutine: null);
  }

  // ==================== STEP OPERATIONS ====================

  /// Update a step in the selected routine
  Future<void> updateStep(AthkarStep updatedStep) async {
    final selectedRoutine = state.selectedRoutine;
    if (selectedRoutine == null) return;

    try {
      await _service.updateStep(selectedRoutine.id, updatedStep);
      
      // Update the step in the local state
      final updatedSteps = selectedRoutine.steps.map((step) {
        return step.id == updatedStep.id ? updatedStep : step;
      }).toList();
      
      final updatedRoutine = selectedRoutine.copyWith(
        steps: updatedSteps,
        updatedAt: DateTime.now(),
      );
      
      state = state.copyWith(selectedRoutine: updatedRoutine);
      
      // Update the routine in the main list
      final updatedRoutines = state.routines.map((routine) {
        return routine.id == updatedRoutine.id ? updatedRoutine : routine;
      }).toList();
      
      state = state.copyWith(routines: updatedRoutines);
      
      // Refresh stats
      await _loadRoutineStats(selectedRoutine.id);
      
      if (kDebugMode) print('Step updated successfully');
    } catch (e) {
      state = state.copyWith(error: 'Failed to update step: $e');
      if (kDebugMode) print('Error updating step: $e');
      rethrow;
    }
  }

  /// Increment a step's count
  Future<void> incrementStep(String stepId) async {
    final selectedRoutine = state.selectedRoutine;
    if (selectedRoutine == null) return;

    final step = selectedRoutine.steps.firstWhere(
      (s) => s.id == stepId,
      orElse: () => throw Exception('Step not found'),
    );

    if (step.canIncrement) {
      final updatedStep = step.increment();
      await updateStep(updatedStep);
    }
  }

  /// Decrement a step's count
  Future<void> decrementStep(String stepId) async {
    final selectedRoutine = state.selectedRoutine;
    if (selectedRoutine == null) return;

    final step = selectedRoutine.steps.firstWhere(
      (s) => s.id == stepId,
      orElse: () => throw Exception('Step not found'),
    );

    if (step.canDecrement) {
      final updatedStep = step.decrement();
      await updateStep(updatedStep);
    }
  }

  /// Reset a step's progress
  Future<void> resetStep(String stepId) async {
    final selectedRoutine = state.selectedRoutine;
    if (selectedRoutine == null) return;

    final step = selectedRoutine.steps.firstWhere(
      (s) => s.id == stepId,
      orElse: () => throw Exception('Step not found'),
    );

    final resetStep = step.reset();
    await updateStep(resetStep);
  }

  /// Complete a step
  Future<void> completeStep(String stepId) async {
    final selectedRoutine = state.selectedRoutine;
    if (selectedRoutine == null) return;

    final step = selectedRoutine.steps.firstWhere(
      (s) => s.id == stepId,
      orElse: () => throw Exception('Step not found'),
    );

    final completedStep = step.complete();
    await updateStep(completedStep);
  }

  // ==================== ROUTINE EXECUTION OPERATIONS ====================

  /// Reset all progress for the selected routine
  Future<void> resetRoutineProgress() async {
    final selectedRoutine = state.selectedRoutine;
    if (selectedRoutine == null) return;

    try {
      await _service.resetRoutineProgress(selectedRoutine.id);
      
      // Update local state
      final resetRoutine = selectedRoutine.resetProgress();
      state = state.copyWith(selectedRoutine: resetRoutine);
      
      // Update the routine in the main list
      final updatedRoutines = state.routines.map((routine) {
        return routine.id == resetRoutine.id ? resetRoutine : routine;
      }).toList();
      
      state = state.copyWith(routines: updatedRoutines);
      
      // Refresh stats
      await _loadRoutineStats(selectedRoutine.id);
      
      if (kDebugMode) print('Routine progress reset successfully');
    } catch (e) {
      state = state.copyWith(error: 'Failed to reset routine progress: $e');
      if (kDebugMode) print('Error resetting routine progress: $e');
      rethrow;
    }
  }

  /// Complete all steps in the selected routine
  Future<void> completeAllSteps() async {
    final selectedRoutine = state.selectedRoutine;
    if (selectedRoutine == null) return;

    try {
      await _service.completeAllSteps(selectedRoutine.id);
      
      // Update local state
      final completedRoutine = selectedRoutine.completeAll();
      state = state.copyWith(selectedRoutine: completedRoutine);
      
      // Update the routine in the main list
      final updatedRoutines = state.routines.map((routine) {
        return routine.id == completedRoutine.id ? completedRoutine : routine;
      }).toList();
      
      state = state.copyWith(routines: updatedRoutines);
      
      // Refresh stats
      await _loadRoutineStats(selectedRoutine.id);
      
      if (kDebugMode) print('All steps completed successfully');
    } catch (e) {
      state = state.copyWith(error: 'Failed to complete all steps: $e');
      if (kDebugMode) print('Error completing all steps: $e');
      rethrow;
    }
  }

  // ==================== FILTERING AND SEARCH ====================

  /// Get routines by category
  List<MultiStepRoutine> getRoutinesByCategory(AthkarCategory category) {
    return state.routines.where((routine) => routine.category == category).toList();
  }

  /// Search routines by name or description
  Future<List<MultiStepRoutine>> searchRoutines(String searchTerm) async {
    try {
      return await _service.searchRoutines(searchTerm);
    } catch (e) {
      if (kDebugMode) print('Error searching routines: $e');
      return [];
    }
  }

  // ==================== STATISTICS ====================

  /// Load statistics for a routine
  Future<void> _loadRoutineStats(String routineId) async {
    try {
      final stats = await _service.getRoutineStats(routineId);
      final updatedStats = Map<String, Map<String, dynamic>>.from(state.routineStats);
      updatedStats[routineId] = stats;
      
      state = state.copyWith(routineStats: updatedStats);
    } catch (e) {
      if (kDebugMode) print('Error loading routine stats: $e');
    }
  }

  /// Get statistics for a specific routine
  Map<String, dynamic>? getRoutineStats(String routineId) {
    return state.routineStats[routineId];
  }

  // ==================== UTILITY METHODS ====================

  /// Clear any error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Get the current step for sequential routines
  AthkarStep? getCurrentStep() {
    final selectedRoutine = state.selectedRoutine;
    if (selectedRoutine == null || !selectedRoutine.isSequential) return null;
    
    return selectedRoutine.currentStep;
  }

  /// Get the current step index for sequential routines
  int? getCurrentStepIndex() {
    final selectedRoutine = state.selectedRoutine;
    if (selectedRoutine == null || !selectedRoutine.isSequential) return null;
    
    return selectedRoutine.currentStepIndex;
  }

  /// Check if the selected routine is completed
  bool isRoutineCompleted() {
    final selectedRoutine = state.selectedRoutine;
    return selectedRoutine?.isCompleted ?? false;
  }
}

// Provider for the multi-step routine notifier
final multiStepRoutineProvider = StateNotifierProvider<MultiStepRoutineNotifier, MultiStepRoutineState>((ref) {
  final service = ref.watch(multiStepRoutineServiceProvider);
  return MultiStepRoutineNotifier(service);
});

// Convenience providers for specific data
final selectedRoutineProvider = Provider<MultiStepRoutine?>((ref) {
  return ref.watch(multiStepRoutineProvider).selectedRoutine;
});

final routinesLoadingProvider = Provider<bool>((ref) {
  return ref.watch(multiStepRoutineProvider).isLoading;
});

final routinesErrorProvider = Provider<String?>((ref) {
  return ref.watch(multiStepRoutineProvider).error;
});

final currentStepProvider = Provider<AthkarStep?>((ref) {
  final notifier = ref.watch(multiStepRoutineProvider.notifier);
  return notifier.getCurrentStep();
});

final currentStepIndexProvider = Provider<int?>((ref) {
  final notifier = ref.watch(multiStepRoutineProvider.notifier);
  return notifier.getCurrentStepIndex();
});
