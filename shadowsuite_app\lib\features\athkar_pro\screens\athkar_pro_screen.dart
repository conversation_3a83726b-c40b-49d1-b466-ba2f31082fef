import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import '../../../core/constants/app_constants.dart';
import '../providers/athkar_providers.dart';
import 'routines_tab.dart';
import 'multi_step_routines_tab.dart';
import 'routine_creation_screen.dart';
import 'progress_tab.dart';
import 'athkar_library_screen.dart';
import 'athkar_settings_screen.dart';
import 'analytics_tab.dart';
import '../widgets/routine_form_dialog.dart';

class AthkarProScreen extends ConsumerWidget {
  const AthkarProScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedTab = ref.watch(athkarTabProvider);

    final List<Widget> tabs = [
      const RoutinesTab(),
      const MultiStepRoutinesTab(),
      const ProgressTab(),
      const AnalyticsTab(),
    ];

    final List<String> tabTitles = [
      'Routines',
      'Multi-Step',
      'Progress',
      'Analytics',
    ];

    return Scaffold(
      body: Column(
        children: [
          // Header
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                ],
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.self_improvement,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: AppConstants.defaultMargin),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Athkar Pro',
                                style: Theme.of(context).textTheme.headlineSmall
                                    ?.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                              Text(
                                tabTitles[selectedTab],
                                style: Theme.of(context).textTheme.bodyMedium
                                    ?.copyWith(
                                      color: Colors.white.withValues(
                                        alpha: 0.9,
                                      ),
                                    ),
                              ),
                            ],
                          ),
                        ),
                        PopupMenuButton<String>(
                          onSelected: (value) {
                            switch (value) {
                              case 'library':
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        const AthkarLibraryScreen(),
                                  ),
                                );
                                break;
                              case 'settings':
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        const AthkarSettingsScreen(),
                                  ),
                                );
                                break;
                            }
                          },
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'library',
                              child: Row(
                                children: [
                                  Icon(Icons.library_books),
                                  SizedBox(width: 8),
                                  Text('Athkar Library'),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'settings',
                              child: Row(
                                children: [
                                  Icon(Icons.settings),
                                  SizedBox(width: 8),
                                  Text('Settings'),
                                ],
                              ),
                            ),
                          ],
                          icon: const Icon(
                            Icons.more_vert,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    Container(
                      padding: const EdgeInsets.all(AppConstants.defaultMargin),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(
                          AppConstants.borderRadius,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: Colors.white.withValues(alpha: 0.8),
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Build consistent habits and track your spiritual routines',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.white.withValues(alpha: 0.9),
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Content
          Expanded(child: tabs[selectedTab]),
        ],
      ),
      // Bottom Navigation
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          boxShadow: [
            BoxShadow(
              blurRadius: 20,
              color: Colors.black.withValues(alpha: 0.1),
            ),
          ],
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
              vertical: AppConstants.defaultMargin,
            ),
            child: GNav(
              rippleColor: Theme.of(
                context,
              ).colorScheme.primary.withValues(alpha: 0.1),
              hoverColor: Theme.of(
                context,
              ).colorScheme.primary.withValues(alpha: 0.1),
              gap: 8,
              activeColor: Theme.of(context).colorScheme.primary,
              iconSize: 24,
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.defaultPadding,
                vertical: AppConstants.defaultMargin,
              ),
              duration: const Duration(milliseconds: 400),
              tabBackgroundColor: Theme.of(
                context,
              ).colorScheme.primary.withValues(alpha: 0.1),
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.6),
              tabs: const [
                GButton(icon: Icons.list, text: 'Routines'),
                GButton(icon: Icons.auto_awesome_motion, text: 'Multi-Step'),
                GButton(icon: Icons.trending_up, text: 'Progress'),
                GButton(icon: Icons.analytics, text: 'Analytics'),
              ],
              selectedIndex: selectedTab,
              onTabChange: (index) {
                ref.read(athkarTabProvider.notifier).state = index;
              },
            ),
          ),
        ),
      ),
      // Floating Action Button
      floatingActionButton:
          (selectedTab == 0 || selectedTab == 1) // Show on both routine tabs
          ? FloatingActionButton(
              onPressed: () => _createRoutine(context, ref, selectedTab),
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  void _createRoutine(BuildContext context, WidgetRef ref, int tabIndex) {
    if (tabIndex == 0) {
      // Old routine system
      ref.read(routineFormProvider.notifier).reset();
      showDialog(
        context: context,
        builder: (context) => const RoutineFormDialog(),
      ).then((_) {
        ref.invalidate(routinesProvider);
      });
    } else if (tabIndex == 1) {
      // Multi-step routine system
      Navigator.of(context).push(
        MaterialPageRoute(builder: (context) => const RoutineCreationScreen()),
      );
    }
  }
}
