import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/constants/app_constants.dart';
import '../models/athkar.dart';
import '../providers/multi_step_routine_provider.dart';
import '../screens/routine_creation_screen.dart';
import '../screens/routine_execution_screen.dart';
import '../widgets/multi_step_routine_card.dart';

class MultiStepRoutinesTab extends ConsumerStatefulWidget {
  const MultiStepRoutinesTab({super.key});

  @override
  ConsumerState<MultiStepRoutinesTab> createState() =>
      _MultiStepRoutinesTabState();
}

class _MultiStepRoutinesTabState extends ConsumerState<MultiStepRoutinesTab> {
  final TextEditingController _searchController = TextEditingController();
  AthkarCategory? _selectedCategory;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final routineState = ref.watch(multiStepRoutineProvider);
    final routines = routineState.routines;

    // Filter routines based on search and category
    final filteredRoutines = routines.where((routine) {
      final matchesSearch =
          _searchController.text.isEmpty ||
          routine.name.toLowerCase().contains(
            _searchController.text.toLowerCase(),
          ) ||
          (routine.description?.toLowerCase().contains(
                _searchController.text.toLowerCase(),
              ) ??
              false);

      final matchesCategory =
          _selectedCategory == null || routine.category == _selectedCategory;

      return matchesSearch && matchesCategory;
    }).toList();

    return Column(
      children: [
        // Search and Filter Bar
        Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              // Search Bar
              TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search routines...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            _searchController.clear();
                            setState(() {});
                          },
                          icon: const Icon(Icons.clear),
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                      AppConstants.borderRadius,
                    ),
                  ),
                ),
                onChanged: (value) => setState(() {}),
              ),
              const SizedBox(height: AppConstants.defaultMargin),
              // Category Filter
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    FilterChip(
                      label: const Text('All'),
                      selected: _selectedCategory == null,
                      onSelected: (selected) {
                        setState(() {
                          _selectedCategory = null;
                        });
                      },
                    ),
                    const SizedBox(width: 8),
                    ...AthkarCategory.values.map((category) {
                      return Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: FilterChip(
                          label: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                _getCategoryIcon(category),
                                size: 16,
                                color: _selectedCategory == category
                                    ? Colors.white
                                    : null,
                              ),
                              const SizedBox(width: 4),
                              Text(_getCategoryDisplayName(category)),
                            ],
                          ),
                          selected: _selectedCategory == category,
                          onSelected: (selected) {
                            setState(() {
                              _selectedCategory = selected ? category : null;
                            });
                          },
                          backgroundColor: _getCategoryColor(
                            category,
                          ).withValues(alpha: 0.1),
                          selectedColor: _getCategoryColor(category),
                          checkmarkColor: Colors.white,
                        ),
                      );
                    }).toList(),
                  ],
                ),
              ),
            ],
          ),
        ),
        // Routines List
        Expanded(
          child: routineState.isLoading
              ? const Center(child: CircularProgressIndicator())
              : routineState.error != null
              ? _buildErrorState(routineState.error!)
              : filteredRoutines.isEmpty
              ? _buildEmptyState()
              : RefreshIndicator(
                  onRefresh: () async {
                    await ref.read(multiStepRoutineProvider.notifier).refresh();
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    itemCount: filteredRoutines.length,
                    itemBuilder: (context, index) {
                      final routine = filteredRoutines[index];
                      return Padding(
                        padding: const EdgeInsets.only(
                          bottom: AppConstants.defaultMargin,
                        ),
                        child: MultiStepRoutineCard(
                          routine: routine,
                          onTap: () => _showRoutineDetails(routine),
                          onExecute: () => _runRoutine(routine),
                          onEdit: () => _editRoutine(routine),
                          onDelete: () => _deleteRoutine(routine),
                        ),
                      );
                    },
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Error loading routines',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.defaultMargin),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(
            onPressed: () {
              ref.read(multiStepRoutineProvider.notifier).refresh();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.self_improvement,
            size: 64,
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No routines yet',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: AppConstants.defaultMargin),
          Text(
            'Create your first multi-step routine to start building consistent habits.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton.icon(
            onPressed: _createRoutine,
            icon: const Icon(Icons.add),
            label: const Text('Create Routine'),
          ),
        ],
      ),
    );
  }

  void _showRoutineDetails(MultiStepRoutine routine) {
    // TODO: Navigate to routine details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Viewing details for ${routine.name}')),
    );
  }

  void _runRoutine(MultiStepRoutine routine) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => RoutineExecutionScreen(routineId: routine.id),
      ),
    );
  }

  void _createRoutine() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const RoutineCreationScreen()),
    );
  }

  void _editRoutine(MultiStepRoutine routine) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => RoutineCreationScreen(editingRoutine: routine),
      ),
    );
  }

  void _deleteRoutine(MultiStepRoutine routine) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Routine'),
        content: Text(
          'Are you sure you want to delete "${routine.name}"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await ref
                    .read(multiStepRoutineProvider.notifier)
                    .deleteRoutine(routine.id);
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('${routine.name} deleted')),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error deleting routine: $e'),
                      backgroundColor: Theme.of(context).colorScheme.error,
                    ),
                  );
                }
              }
            },
            child: Text(
              'Delete',
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(AthkarCategory category) {
    switch (category) {
      case AthkarCategory.morning:
        return Icons.wb_sunny;
      case AthkarCategory.evening:
        return Icons.nights_stay;
      case AthkarCategory.prayer:
        return Icons.mosque;
      case AthkarCategory.sleep:
        return Icons.bedtime;
      case AthkarCategory.travel:
        return Icons.flight;
      case AthkarCategory.general:
        return Icons.auto_awesome;
    }
  }

  String _getCategoryDisplayName(AthkarCategory category) {
    switch (category) {
      case AthkarCategory.morning:
        return 'Morning';
      case AthkarCategory.evening:
        return 'Evening';
      case AthkarCategory.prayer:
        return 'Prayer';
      case AthkarCategory.sleep:
        return 'Sleep';
      case AthkarCategory.travel:
        return 'Travel';
      case AthkarCategory.general:
        return 'General';
    }
  }

  Color _getCategoryColor(AthkarCategory category) {
    switch (category) {
      case AthkarCategory.morning:
        return Colors.orange;
      case AthkarCategory.evening:
        return Colors.indigo;
      case AthkarCategory.prayer:
        return Colors.green;
      case AthkarCategory.sleep:
        return Colors.purple;
      case AthkarCategory.travel:
        return Colors.blue;
      case AthkarCategory.general:
        return Colors.teal;
    }
  }
}
