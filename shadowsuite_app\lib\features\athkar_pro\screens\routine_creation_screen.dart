import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_theme.dart';
// import '../../../core/widgets/custom_app_bar.dart'; // Will create this widget
import '../models/athkar.dart';
import '../providers/multi_step_routine_provider.dart';
import '../widgets/step_builder_widget.dart';

class RoutineCreationScreen extends ConsumerStatefulWidget {
  final MultiStepRoutine? editingRoutine;

  const RoutineCreationScreen({super.key, this.editingRoutine});

  @override
  ConsumerState<RoutineCreationScreen> createState() =>
      _RoutineCreationScreenState();
}

class _RoutineCreationScreenState extends ConsumerState<RoutineCreationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  AthkarCategory _selectedCategory = AthkarCategory.general;
  Color? _selectedColor;
  bool _isSequential = true;
  List<AthkarStep> _steps = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.editingRoutine != null) {
      final routine = widget.editingRoutine!;
      _nameController.text = routine.name;
      _descriptionController.text = routine.description ?? '';
      _selectedCategory = routine.category;
      _selectedColor = routine.color;
      _isSequential = routine.isSequential;
      _steps = List.from(routine.steps);
    } else {
      // Add a default step for new routines
      _addDefaultStep();
    }
  }

  void _addDefaultStep() {
    _steps.add(
      AthkarStep(
        templateId: 'custom',
        arabicText: '',
        transliteration: '',
        translation: '',
        targetCount: 1,
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.editingRoutine != null ? 'Edit Routine' : 'Create Routine',
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveRoutine,
            child: Text(
              'Save',
              style: TextStyle(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildBasicInfoSection(),
                    const SizedBox(height: 24),
                    _buildCategorySection(),
                    const SizedBox(height: 24),
                    _buildColorSection(),
                    const SizedBox(height: 24),
                    _buildSequentialSection(),
                    const SizedBox(height: 24),
                    _buildStepsSection(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Basic Information',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Routine Name *',
                hintText: 'Enter routine name',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a routine name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
                hintText: 'Enter routine description',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Category',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: AthkarCategory.values.map((category) {
                final isSelected = _selectedCategory == category;
                return FilterChip(
                  label: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getCategoryIcon(category),
                        size: 16,
                        color: isSelected ? Colors.white : null,
                      ),
                      const SizedBox(width: 4),
                      Text(_getCategoryDisplayName(category)),
                    ],
                  ),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _selectedCategory = category;
                      });
                    }
                  },
                  backgroundColor: _getCategoryColor(
                    category,
                  ).withValues(alpha: 0.1),
                  selectedColor: _getCategoryColor(category),
                  checkmarkColor: Colors.white,
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Color Theme (Optional)',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildColorOption(null, 'Default'),
                _buildColorOption(Colors.red, 'Red'),
                _buildColorOption(Colors.blue, 'Blue'),
                _buildColorOption(Colors.green, 'Green'),
                _buildColorOption(Colors.orange, 'Orange'),
                _buildColorOption(Colors.purple, 'Purple'),
                _buildColorOption(Colors.teal, 'Teal'),
                _buildColorOption(Colors.indigo, 'Indigo'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorOption(Color? color, String label) {
    final isSelected = _selectedColor == color;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedColor = color;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color:
              color?.withValues(alpha: 0.1) ??
              Colors.grey.withValues(alpha: 0.1),
          border: Border.all(
            color: isSelected
                ? (color ?? Theme.of(context).colorScheme.primary)
                : Colors.transparent,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                color: color ?? Colors.grey,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Text(label),
          ],
        ),
      ),
    );
  }

  Widget _buildSequentialSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Execution Mode',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            SwitchListTile(
              title: const Text('Sequential Execution'),
              subtitle: Text(
                _isSequential
                    ? 'Steps must be completed in order'
                    : 'Steps can be completed in any order',
              ),
              value: _isSequential,
              onChanged: (value) {
                setState(() {
                  _isSequential = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStepsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Steps (${_steps.length})',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _addStep,
                  icon: const Icon(Icons.add),
                  label: const Text('Add Step'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_steps.isEmpty)
              const Center(
                child: Text('No steps added yet. Add your first step!'),
              )
            else
              StepBuilderWidget(
                steps: _steps,
                onStepsChanged: (updatedSteps) {
                  setState(() {
                    _steps = updatedSteps;
                  });
                },
              ),
          ],
        ),
      ),
    );
  }

  void _addStep() {
    setState(() {
      _steps.add(
        AthkarStep(
          templateId: 'custom',
          arabicText: '',
          transliteration: '',
          translation: '',
          targetCount: 1,
        ),
      );
    });
  }

  Future<void> _saveRoutine() async {
    if (!_formKey.currentState!.validate()) return;

    if (_steps.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add at least one step')),
      );
      return;
    }

    // Validate all steps
    for (int i = 0; i < _steps.length; i++) {
      final step = _steps[i];
      if (step.arabicText.trim().isEmpty || step.translation.trim().isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Please complete step ${i + 1}')),
        );
        return;
      }
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final routine = MultiStepRoutine(
        id: widget.editingRoutine?.id,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        steps: _steps,
        category: _selectedCategory,
        color: _selectedColor,
        isSequential: _isSequential,
        createdAt: widget.editingRoutine?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (widget.editingRoutine != null) {
        await ref
            .read(multiStepRoutineProvider.notifier)
            .updateRoutine(routine);
      } else {
        await ref.read(multiStepRoutineProvider.notifier).addRoutine(routine);
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.editingRoutine != null
                  ? 'Routine updated successfully'
                  : 'Routine created successfully',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error saving routine: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  IconData _getCategoryIcon(AthkarCategory category) {
    switch (category) {
      case AthkarCategory.morning:
        return Icons.wb_sunny;
      case AthkarCategory.evening:
        return Icons.nights_stay;
      case AthkarCategory.prayer:
        return Icons.mosque;
      case AthkarCategory.sleep:
        return Icons.bedtime;
      case AthkarCategory.travel:
        return Icons.flight;
      case AthkarCategory.general:
        return Icons.auto_awesome;
    }
  }

  String _getCategoryDisplayName(AthkarCategory category) {
    switch (category) {
      case AthkarCategory.morning:
        return 'Morning';
      case AthkarCategory.evening:
        return 'Evening';
      case AthkarCategory.prayer:
        return 'Prayer';
      case AthkarCategory.sleep:
        return 'Sleep';
      case AthkarCategory.travel:
        return 'Travel';
      case AthkarCategory.general:
        return 'General';
    }
  }

  Color _getCategoryColor(AthkarCategory category) {
    switch (category) {
      case AthkarCategory.morning:
        return Colors.orange;
      case AthkarCategory.evening:
        return Colors.indigo;
      case AthkarCategory.prayer:
        return Colors.green;
      case AthkarCategory.sleep:
        return Colors.purple;
      case AthkarCategory.travel:
        return Colors.blue;
      case AthkarCategory.general:
        return Colors.teal;
    }
  }
}
