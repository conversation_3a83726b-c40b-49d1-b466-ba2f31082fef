import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/athkar.dart';
import '../providers/multi_step_routine_provider.dart';

class RoutineExecutionScreen extends ConsumerStatefulWidget {
  final String routineId;

  const RoutineExecutionScreen({
    super.key,
    required this.routineId,
  });

  @override
  ConsumerState<RoutineExecutionScreen> createState() => _RoutineExecutionScreenState();
}

class _RoutineExecutionScreenState extends ConsumerState<RoutineExecutionScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadRoutine();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
  }

  Future<void> _loadRoutine() async {
    await ref.read(multiStepRoutineProvider.notifier).selectRoutine(widget.routineId);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final routine = ref.watch(selectedRoutineProvider);
    final currentStep = ref.watch(currentStepProvider);
    final currentStepIndex = ref.watch(currentStepIndexProvider);
    final isLoading = ref.watch(routinesLoadingProvider);

    if (isLoading || routine == null) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(routine.name),
        actions: [
          IconButton(
            onPressed: _showRoutineMenu,
            icon: const Icon(Icons.more_vert),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildProgressHeader(routine),
          Expanded(
            child: routine.isSequential
                ? _buildSequentialView(routine, currentStep, currentStepIndex)
                : _buildNonSequentialView(routine),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomControls(routine, currentStep),
    );
  }

  Widget _buildProgressHeader(MultiStepRoutine routine) {
    final completedSteps = routine.steps.where((step) => step.isCompleted).length;
    final totalSteps = routine.steps.length;
    final progress = totalSteps > 0 ? completedSteps / totalSteps : 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Progress',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '$completedSteps / $totalSteps steps',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.white.withValues(alpha: 0.3),
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
          if (routine.isCompleted)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.celebration,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Routine Completed! 🎉',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSequentialView(MultiStepRoutine routine, AthkarStep? currentStep, int? currentStepIndex) {
    if (currentStep == null || currentStepIndex == null) {
      return const Center(
        child: Text('No current step available'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildStepCard(currentStep, currentStepIndex + 1, routine.steps.length, true),
          const SizedBox(height: 16),
          _buildStepsList(routine, currentStepIndex),
        ],
      ),
    );
  }

  Widget _buildNonSequentialView(MultiStepRoutine routine) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: routine.steps.length,
      itemBuilder: (context, index) {
        final step = routine.steps[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildStepCard(step, index + 1, routine.steps.length, false),
        );
      },
    );
  }

  Widget _buildStepCard(AthkarStep step, int stepNumber, int totalSteps, bool isCurrent) {
    final progress = step.targetCount > 0 ? step.currentCount / step.targetCount : 0.0;
    
    return AnimatedBuilder(
      animation: isCurrent ? _pulseAnimation : const AlwaysStoppedAnimation(1.0),
      builder: (context, child) {
        return Transform.scale(
          scale: isCurrent ? _pulseAnimation.value : 1.0,
          child: Card(
            elevation: isCurrent ? 8 : 2,
            color: step.isCompleted 
                ? Theme.of(context).colorScheme.primaryContainer
                : null,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 16,
                        backgroundColor: step.isCompleted
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.outline,
                        child: step.isCompleted
                            ? const Icon(Icons.check, color: Colors.white, size: 16)
                            : Text(
                                stepNumber.toString(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Step $stepNumber of $totalSteps',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      if (isCurrent)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            'Current',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  // Arabic Text
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      step.arabicText,
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                      textDirection: TextDirection.rtl,
                    ),
                  ),
                  
                  if (step.transliteration.isNotEmpty) ...[
                    const SizedBox(height: 12),
                    Text(
                      step.transliteration,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontStyle: FontStyle.italic,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                  
                  const SizedBox(height: 12),
                  Text(
                    step.translation,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Counter Section
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      IconButton(
                        onPressed: step.canDecrement 
                            ? () => _decrementStep(step.id)
                            : null,
                        icon: const Icon(Icons.remove_circle_outline),
                        iconSize: 32,
                      ),
                      const SizedBox(width: 16),
                      Column(
                        children: [
                          Text(
                            '${step.currentCount}',
                            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          Text(
                            'of ${step.targetCount}',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                      const SizedBox(width: 16),
                      IconButton(
                        onPressed: step.canIncrement 
                            ? () => _incrementStep(step.id)
                            : null,
                        icon: const Icon(Icons.add_circle_outline),
                        iconSize: 32,
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 12),
                  LinearProgressIndicator(
                    value: progress,
                    backgroundColor: Colors.grey.withValues(alpha: 0.3),
                    valueColor: AlwaysStoppedAnimation<Color>(
                      step.isCompleted 
                          ? Colors.green
                          : Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStepsList(MultiStepRoutine routine, int currentStepIndex) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'All Steps',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...routine.steps.asMap().entries.map((entry) {
              final index = entry.key;
              final step = entry.value;
              final isCurrent = index == currentStepIndex;
              
              return ListTile(
                leading: CircleAvatar(
                  radius: 12,
                  backgroundColor: step.isCompleted
                      ? Colors.green
                      : isCurrent
                          ? Theme.of(context).colorScheme.primary
                          : Colors.grey,
                  child: step.isCompleted
                      ? const Icon(Icons.check, color: Colors.white, size: 16)
                      : Text(
                          '${index + 1}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
                title: Text(
                  step.arabicText.length > 30 
                      ? '${step.arabicText.substring(0, 30)}...'
                      : step.arabicText,
                  style: TextStyle(
                    fontWeight: isCurrent ? FontWeight.bold : FontWeight.normal,
                    color: isCurrent 
                        ? Theme.of(context).colorScheme.primary
                        : null,
                  ),
                  textDirection: TextDirection.rtl,
                ),
                subtitle: Text('${step.currentCount} / ${step.targetCount}'),
                trailing: step.isCompleted
                    ? const Icon(
                        Icons.check_circle,
                        color: Colors.green,
                      )
                    : isCurrent
                        ? Icon(
                            Icons.play_circle_filled,
                            color: Theme.of(context).colorScheme.primary,
                          )
                        : null,
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomControls(MultiStepRoutine routine, AthkarStep? currentStep) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: _resetProgress,
              child: const Text('Reset'),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: routine.isCompleted 
                  ? _completeRoutine
                  : currentStep?.isCompleted == true
                      ? _nextStep
                      : null,
              child: Text(
                routine.isCompleted 
                    ? 'Complete Routine'
                    : currentStep?.isCompleted == true
                        ? 'Next Step'
                        : 'Complete Current Step',
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _incrementStep(String stepId) async {
    await ref.read(multiStepRoutineProvider.notifier).incrementStep(stepId);
  }

  Future<void> _decrementStep(String stepId) async {
    await ref.read(multiStepRoutineProvider.notifier).decrementStep(stepId);
  }

  Future<void> _resetProgress() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Progress'),
        content: const Text('Are you sure you want to reset all progress for this routine?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Reset'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await ref.read(multiStepRoutineProvider.notifier).resetRoutineProgress();
    }
  }

  void _nextStep() {
    // Auto-advance to next step in sequential mode
    // This is handled automatically by the provider when a step is completed
  }

  void _completeRoutine() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Congratulations! 🎉'),
        content: const Text('You have completed this routine successfully!'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Go back to routine list
            },
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  void _showRoutineMenu() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.refresh),
            title: const Text('Reset Progress'),
            onTap: () {
              Navigator.of(context).pop();
              _resetProgress();
            },
          ),
          ListTile(
            leading: const Icon(Icons.check_circle),
            title: const Text('Complete All Steps'),
            onTap: () async {
              Navigator.of(context).pop();
              await ref.read(multiStepRoutineProvider.notifier).completeAllSteps();
            },
          ),
          ListTile(
            leading: const Icon(Icons.edit),
            title: const Text('Edit Routine'),
            onTap: () {
              Navigator.of(context).pop();
              // Navigate to edit screen
            },
          ),
        ],
      ),
    );
  }
}
