import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/constants/app_constants.dart';
import '../providers/athkar_providers.dart';
import '../models/routine.dart';
import '../widgets/routine_card.dart';
import '../widgets/routine_form_dialog.dart';

class RoutinesTab extends ConsumerStatefulWidget {
  const RoutinesTab({super.key});

  @override
  ConsumerState<RoutinesTab> createState() => _RoutinesTabState();
}

class _RoutinesTabState extends ConsumerState<RoutinesTab> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      ref.read(routineSearchProvider.notifier).state = _searchController.text;
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final routinesAsync = ref.watch(filteredRoutinesProvider);
    final filterState = ref.watch(routineFilterProvider);

    return Column(
      children: [
        // Search and Filter Bar
        Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              // Search Bar
              TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search routines...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            _searchController.clear();
                          },
                          icon: const Icon(Icons.clear),
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                      AppConstants.borderRadius,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: AppConstants.defaultMargin),
              // Filter Chips
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    FilterChip(
                      label: const Text('Active Only'),
                      selected: filterState.showActiveOnly,
                      onSelected: (selected) {
                        ref
                            .read(routineFilterProvider.notifier)
                            .setShowActiveOnly(selected);
                      },
                    ),
                    const SizedBox(width: 8),
                    FilterChip(
                      label: const Text('Completed'),
                      selected: filterState.showCompletedOnly,
                      onSelected: (selected) {
                        ref
                            .read(routineFilterProvider.notifier)
                            .setShowCompletedOnly(selected);
                      },
                    ),
                    const SizedBox(width: 8),
                    PopupMenuButton<RoutineType>(
                      child: Chip(
                        label: Text(
                          filterState.typeFilter?.name.toUpperCase() ??
                              'ALL TYPES',
                        ),
                        deleteIcon: filterState.typeFilter != null
                            ? const Icon(Icons.close, size: 16)
                            : null,
                        onDeleted: filterState.typeFilter != null
                            ? () => ref
                                  .read(routineFilterProvider.notifier)
                                  .setTypeFilter(null)
                            : null,
                      ),
                      onSelected: (type) {
                        ref
                            .read(routineFilterProvider.notifier)
                            .setTypeFilter(type);
                      },
                      itemBuilder: (context) => RoutineType.values.map((type) {
                        return PopupMenuItem(
                          value: type,
                          child: Text(type.name.toUpperCase()),
                        );
                      }).toList(),
                    ),
                    if (filterState.hasActiveFilters) ...[
                      const SizedBox(width: 8),
                      ActionChip(
                        label: const Text('Clear Filters'),
                        onPressed: () {
                          ref
                              .read(routineFilterProvider.notifier)
                              .clearFilters();
                        },
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
        // Routines List
        Expanded(
          child: routinesAsync.when(
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  Text(
                    'Error loading routines',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: AppConstants.defaultMargin),
                  Text(
                    error.toString(),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  ElevatedButton(
                    onPressed: () {
                      ref.invalidate(filteredRoutinesProvider);
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
            data: (routines) {
              // Apply additional filters
              var filteredRoutines = routines;

              if (filterState.showActiveOnly) {
                filteredRoutines = filteredRoutines
                    .where((r) => r.status == RoutineStatus.active)
                    .toList();
              }

              if (filterState.showCompletedOnly) {
                filteredRoutines = filteredRoutines
                    .where((r) => r.isCompleted)
                    .toList();
              }

              if (filterState.typeFilter != null) {
                filteredRoutines = filteredRoutines
                    .where((r) => r.type == filterState.typeFilter)
                    .toList();
              }

              if (filteredRoutines.isEmpty) {
                return _buildEmptyState(context);
              }

              return RefreshIndicator(
                onRefresh: () async {
                  ref.invalidate(filteredRoutinesProvider);
                },
                child: ListView.builder(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  itemCount: filteredRoutines.length,
                  itemBuilder: (context, index) {
                    final routine = filteredRoutines[index];
                    return Padding(
                      padding: const EdgeInsets.only(
                        bottom: AppConstants.defaultMargin,
                      ),
                      child: RoutineCard(
                        routine: routine,
                        onTap: () => _showRoutineDetails(context, routine),
                        onRun: () => _runRoutine(context, routine),
                        onComplete: () =>
                            _completeRoutine(context, ref, routine),
                        onEdit: () => _editRoutine(context, ref, routine),
                        onDelete: () => _deleteRoutine(context, ref, routine),
                      ),
                    );
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.self_improvement,
            size: 64,
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No routines yet',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: AppConstants.defaultMargin),
          Text(
            'Create your first routine to start building consistent habits.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton.icon(
            onPressed: () => _createRoutine(context, ref),
            icon: const Icon(Icons.add),
            label: const Text('Create Routine'),
          ),
        ],
      ),
    );
  }

  void _showRoutineDetails(BuildContext context, Routine routine) {
    // TODO: Navigate to routine details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Viewing details for ${routine.name}')),
    );
  }

  void _runRoutine(BuildContext context, Routine routine) {
    // For now, show a placeholder message since we need to integrate with multi-step routines
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Running ${routine.name}...'),
        action: SnackBarAction(
          label: 'Start',
          onPressed: () {
            // TODO: Navigate to routine execution screen
            // This will be implemented when we integrate with multi-step routines
          },
        ),
      ),
    );
  }

  void _completeRoutine(
    BuildContext context,
    WidgetRef ref,
    Routine routine,
  ) async {
    try {
      final service = ref.read(athkarServiceProvider);
      await service.completeRoutine(routine.id);

      // Refresh the routines list
      ref.invalidate(filteredRoutinesProvider);

      if (context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('${routine.name} completed!')));
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error completing routine: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _createRoutine(BuildContext context, WidgetRef ref) {
    ref.read(routineFormProvider.notifier).reset();
    showDialog(
      context: context,
      builder: (context) => const RoutineFormDialog(),
    ).then((_) {
      ref.invalidate(filteredRoutinesProvider);
    });
  }

  void _editRoutine(BuildContext context, WidgetRef ref, Routine routine) {
    ref.read(routineFormProvider.notifier).loadRoutine(routine);
    showDialog(
      context: context,
      builder: (context) => RoutineFormDialog(routine: routine),
    ).then((_) {
      ref.invalidate(filteredRoutinesProvider);
    });
  }

  void _deleteRoutine(BuildContext context, WidgetRef ref, Routine routine) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Routine'),
        content: Text(
          'Are you sure you want to delete "${routine.name}"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                final service = ref.read(athkarServiceProvider);
                await service.deleteRoutine(routine.id);
                ref.invalidate(filteredRoutinesProvider);

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('${routine.name} deleted')),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error deleting routine: $e'),
                      backgroundColor: Theme.of(context).colorScheme.error,
                    ),
                  );
                }
              }
            },
            child: Text(
              'Delete',
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          ),
        ],
      ),
    );
  }
}
