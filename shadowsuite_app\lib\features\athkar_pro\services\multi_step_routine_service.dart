import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/database/platform_database.dart';
import '../../../core/services/supabase_service.dart';
import '../models/athkar.dart';

class MultiStepRoutineService {
  final PlatformDatabase _db = DatabaseFactory.getInstance();
  final SupabaseService _supabase = SupabaseService();

  Future<void> initialize() async {
    await _db.initialize();
    // Supabase is already initialized in main.dart
  }

  // ==================== CRUD OPERATIONS ====================

  /// Create a new multi-step routine
  Future<String> createRoutine(MultiStepRoutine routine) async {
    try {
      final routineData = routine.toMap();

      // Remove steps from routine data as they're stored separately
      final steps = routineData.remove('steps') as List<dynamic>?;

      // Insert the routine
      final routineId = await _insertOrUpdate(
        'multi_step_routines',
        routineData,
      );

      // Insert the steps
      if (steps != null) {
        for (int i = 0; i < steps.length; i++) {
          final stepData = Map<String, dynamic>.from(steps[i]);
          stepData['routine_id'] = routineId;
          stepData['step_order'] = i;
          await _insertOrUpdate('athkar_steps', stepData);
        }
      }

      // Sync to Supabase
      await _syncToSupabase(routine);

      return routineId;
    } catch (e) {
      if (kDebugMode) print('Error creating routine: $e');
      rethrow;
    }
  }

  /// Update an existing multi-step routine
  Future<void> updateRoutine(MultiStepRoutine routine) async {
    try {
      final routineData = routine.toMap();

      // Remove steps from routine data
      final steps = routineData.remove('steps') as List<dynamic>?;

      // Update the routine
      await _db.update(
        'multi_step_routines',
        routineData,
        where: 'id = ?',
        whereArgs: [routine.id],
      );

      // Delete existing steps
      await _db.delete(
        'athkar_steps',
        where: 'routine_id = ?',
        whereArgs: [routine.id],
      );

      // Insert updated steps
      if (steps != null) {
        for (int i = 0; i < steps.length; i++) {
          final stepData = Map<String, dynamic>.from(steps[i]);
          stepData['routine_id'] = routine.id;
          stepData['step_order'] = i;
          await _insertOrUpdate('athkar_steps', stepData);
        }
      }
    } catch (e) {
      if (kDebugMode) print('Error updating routine: $e');
      rethrow;
    }
  }

  /// Delete a multi-step routine and all its steps
  Future<void> deleteRoutine(String routineId) async {
    try {
      // Delete all steps first
      await _db.delete(
        'athkar_steps',
        where: 'routine_id = ?',
        whereArgs: [routineId],
      );

      // Delete the routine
      await _db.delete(
        'multi_step_routines',
        where: 'id = ?',
        whereArgs: [routineId],
      );
    } catch (e) {
      if (kDebugMode) print('Error deleting routine: $e');
      rethrow;
    }
  }

  /// Get all multi-step routines
  Future<List<MultiStepRoutine>> getAllRoutines() async {
    try {
      // Try to sync from Supabase if online
      await _syncFromSupabase();

      final routineResults = await _db.select('multi_step_routines');
      final routines = <MultiStepRoutine>[];

      for (final routineData in routineResults) {
        final steps = await _getStepsForRoutine(routineData['id'] as String);
        final routineWithSteps = Map<String, dynamic>.from(routineData);
        routineWithSteps['steps'] = steps.map((step) => step.toMap()).toList();

        routines.add(MultiStepRoutine.fromMap(routineWithSteps));
      }

      return routines;
    } catch (e) {
      if (kDebugMode) print('Error getting all routines: $e');
      return [];
    }
  }

  /// Get a specific routine by ID
  Future<MultiStepRoutine?> getRoutineById(String routineId) async {
    try {
      final routineResults = await _db.select(
        'multi_step_routines',
        where: 'id = ?',
        whereArgs: [routineId],
      );

      if (routineResults.isEmpty) return null;

      final routineData = routineResults.first;
      final steps = await _getStepsForRoutine(routineId);

      final routineWithSteps = Map<String, dynamic>.from(routineData);
      routineWithSteps['steps'] = steps.map((step) => step.toMap()).toList();

      return MultiStepRoutine.fromMap(routineWithSteps);
    } catch (e) {
      if (kDebugMode) print('Error getting routine by ID: $e');
      return null;
    }
  }

  /// Get routines by category
  Future<List<MultiStepRoutine>> getRoutinesByCategory(
    AthkarCategory category,
  ) async {
    try {
      final routineResults = await _db.select(
        'multi_step_routines',
        where: 'category = ?',
        whereArgs: [category.name],
      );

      final routines = <MultiStepRoutine>[];

      for (final routineData in routineResults) {
        final steps = await _getStepsForRoutine(routineData['id'] as String);
        final routineWithSteps = Map<String, dynamic>.from(routineData);
        routineWithSteps['steps'] = steps.map((step) => step.toMap()).toList();

        routines.add(MultiStepRoutine.fromMap(routineWithSteps));
      }

      return routines;
    } catch (e) {
      if (kDebugMode) print('Error getting routines by category: $e');
      return [];
    }
  }

  // ==================== STEP OPERATIONS ====================

  /// Update a specific step in a routine
  Future<void> updateStep(String routineId, AthkarStep updatedStep) async {
    try {
      final stepData = updatedStep.toMap();
      stepData['routine_id'] = routineId;

      await _db.update(
        'athkar_steps',
        stepData,
        where: 'id = ?',
        whereArgs: [updatedStep.id],
      );
    } catch (e) {
      if (kDebugMode) print('Error updating step: $e');
      rethrow;
    }
  }

  /// Get all steps for a specific routine
  Future<List<AthkarStep>> _getStepsForRoutine(String routineId) async {
    try {
      final stepResults = await _db.select(
        'athkar_steps',
        where: 'routine_id = ?',
        whereArgs: [routineId],
      );

      // Sort by step_order
      stepResults.sort(
        (a, b) => (a['step_order'] as int).compareTo(b['step_order'] as int),
      );

      return stepResults
          .map((stepData) => AthkarStep.fromMap(stepData))
          .toList();
    } catch (e) {
      if (kDebugMode) print('Error getting steps for routine: $e');
      return [];
    }
  }

  // ==================== EXECUTION OPERATIONS ====================

  /// Reset all progress for a routine
  Future<void> resetRoutineProgress(String routineId) async {
    try {
      await _db.update(
        'athkar_steps',
        {
          'current_count': 0,
          'is_completed': 0,
          'completed_at': null,
          'updated_at': DateTime.now().millisecondsSinceEpoch,
        },
        where: 'routine_id = ?',
        whereArgs: [routineId],
      );
    } catch (e) {
      if (kDebugMode) print('Error resetting routine progress: $e');
      rethrow;
    }
  }

  /// Mark all steps as completed
  Future<void> completeAllSteps(String routineId) async {
    try {
      final now = DateTime.now().millisecondsSinceEpoch;

      // Get all steps to update their target counts
      final steps = await _getStepsForRoutine(routineId);

      for (final step in steps) {
        await _db.update(
          'athkar_steps',
          {
            'current_count': step.targetCount,
            'is_completed': 1,
            'completed_at': now,
            'updated_at': now,
          },
          where: 'id = ?',
          whereArgs: [step.id],
        );
      }
    } catch (e) {
      if (kDebugMode) print('Error completing all steps: $e');
      rethrow;
    }
  }

  // ==================== HELPER METHODS ====================

  /// Insert or update based on whether record exists
  Future<String> _insertOrUpdate(
    String table,
    Map<String, dynamic> data,
  ) async {
    final id = data['id'] as String?;

    if (id != null) {
      final existing = await _db.select(
        table,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (existing.isNotEmpty) {
        // Update existing record
        data['updated_at'] = DateTime.now().millisecondsSinceEpoch;
        await _db.update(table, data, where: 'id = ?', whereArgs: [id]);
        return id;
      }
    }

    // Insert new record
    final newId = id ?? DateTime.now().millisecondsSinceEpoch.toString();
    data['id'] = newId;
    data['created_at'] = DateTime.now().millisecondsSinceEpoch;
    data['updated_at'] = DateTime.now().millisecondsSinceEpoch;

    await _db.insert(table, data);
    return newId;
  }

  /// Search routines by name or description
  Future<List<MultiStepRoutine>> searchRoutines(String searchTerm) async {
    try {
      final routineResults = await _db.search(
        'multi_step_routines',
        'name',
        searchTerm,
      );
      final routines = <MultiStepRoutine>[];

      for (final routineData in routineResults) {
        final steps = await _getStepsForRoutine(routineData['id'] as String);
        final routineWithSteps = Map<String, dynamic>.from(routineData);
        routineWithSteps['steps'] = steps.map((step) => step.toMap()).toList();

        routines.add(MultiStepRoutine.fromMap(routineWithSteps));
      }

      return routines;
    } catch (e) {
      if (kDebugMode) print('Error searching routines: $e');
      return [];
    }
  }

  /// Get routine statistics
  Future<Map<String, dynamic>> getRoutineStats(String routineId) async {
    try {
      final steps = await _getStepsForRoutine(routineId);

      final totalSteps = steps.length;
      final completedSteps = steps.where((step) => step.isCompleted).length;
      final totalTargetCount = steps.fold<int>(
        0,
        (sum, step) => sum + step.targetCount,
      );
      final totalCurrentCount = steps.fold<int>(
        0,
        (sum, step) => sum + step.currentCount,
      );

      return {
        'total_steps': totalSteps,
        'completed_steps': completedSteps,
        'remaining_steps': totalSteps - completedSteps,
        'total_target_count': totalTargetCount,
        'total_current_count': totalCurrentCount,
        'overall_progress': totalSteps > 0 ? completedSteps / totalSteps : 0.0,
        'count_progress': totalTargetCount > 0
            ? totalCurrentCount / totalTargetCount
            : 0.0,
      };
    } catch (e) {
      if (kDebugMode) print('Error getting routine stats: $e');
      return {};
    }
  }

  // ==================== SUPABASE SYNC ====================

  /// Sync routines from Supabase to local database
  Future<void> _syncFromSupabase() async {
    if (!_supabase.isInitialized) return;

    try {
      final isOnline = await _supabase.isOnline();
      if (!isOnline) return;

      final supabaseRoutines = await _supabase.getMultiStepRoutines();

      for (final routineData in supabaseRoutines) {
        // Extract steps from the routine data
        final steps = routineData['athkar_steps'] as List<dynamic>? ?? [];
        final routineDataWithoutSteps = Map<String, dynamic>.from(routineData);
        routineDataWithoutSteps.remove('athkar_steps');

        // Insert or update routine
        await _insertOrUpdate('multi_step_routines', routineDataWithoutSteps);

        // Insert or update steps
        for (int i = 0; i < steps.length; i++) {
          final stepData = Map<String, dynamic>.from(steps[i]);
          stepData['routine_id'] = routineData['id'];
          stepData['step_order'] = i;
          await _insertOrUpdate('athkar_steps', stepData);
        }
      }

      if (kDebugMode) {
        print('✅ Synced ${supabaseRoutines.length} routines from Supabase');
      }
    } catch (e) {
      if (kDebugMode) print('⚠️ Error syncing from Supabase: $e');
      // Continue with local data if sync fails
    }
  }

  /// Sync a routine to Supabase
  Future<void> _syncToSupabase(MultiStepRoutine routine) async {
    if (!_supabase.isInitialized) return;

    try {
      final isOnline = await _supabase.isOnline();
      if (!isOnline) return;

      final routineData = routine.toMap();
      final steps = routineData.remove('steps') as List<dynamic>? ?? [];

      // Convert steps to the format expected by Supabase
      final stepMaps = steps.map((step) {
        final stepMap = Map<String, dynamic>.from(step);
        stepMap['routine_id'] = routine.id;
        return stepMap;
      }).toList();

      await _supabase.createMultiStepRoutine(routineData, stepMaps);

      if (kDebugMode) print('✅ Synced routine to Supabase: ${routine.name}');
    } catch (e) {
      if (kDebugMode) print('⚠️ Error syncing to Supabase: $e');
      // Continue with local operation if sync fails
    }
  }
}
