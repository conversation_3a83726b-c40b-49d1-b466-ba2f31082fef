import 'package:flutter/material.dart';
import '../models/athkar.dart';

class StepBuilderWidget extends StatefulWidget {
  final List<AthkarStep> steps;
  final Function(List<AthkarStep>) onStepsChanged;

  const StepBuilderWidget({
    super.key,
    required this.steps,
    required this.onStepsChanged,
  });

  @override
  State<StepBuilderWidget> createState() => _StepBuilderWidgetState();
}

class _StepBuilderWidgetState extends State<StepBuilderWidget> {
  late List<AthkarStep> _steps;
  final List<GlobalKey<FormState>> _formKeys = [];

  @override
  void initState() {
    super.initState();
    _steps = List.from(widget.steps);
    _initializeFormKeys();
  }

  @override
  void didUpdateWidget(StepBuilderWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.steps != oldWidget.steps) {
      _steps = List.from(widget.steps);
      _initializeFormKeys();
    }
  }

  void _initializeFormKeys() {
    _formKeys.clear();
    for (int i = 0; i < _steps.length; i++) {
      _formKeys.add(GlobalKey<FormState>());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ReorderableListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _steps.length,
          onReorder: _reorderSteps,
          itemBuilder: (context, index) {
            return _buildStepCard(index);
          },
        ),
      ],
    );
  }

  Widget _buildStepCard(int index) {
    final step = _steps[index];

    return Card(
      key: ValueKey(step.id),
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKeys[index],
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.drag_handle, color: Colors.grey[600]),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Step ${index + 1}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => _removeStep(index),
                    icon: const Icon(Icons.delete_outline),
                    color: Colors.red,
                    tooltip: 'Remove step',
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Arabic Text Field
              TextFormField(
                initialValue: step.arabicText,
                decoration: const InputDecoration(
                  labelText: 'Arabic Text *',
                  hintText: 'Enter the Arabic dhikr text',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
                textDirection: TextDirection.rtl,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter Arabic text';
                  }
                  return null;
                },
                onChanged: (value) => _updateStep(index, arabicText: value),
              ),
              const SizedBox(height: 12),

              // Transliteration Field
              TextFormField(
                initialValue: step.transliteration,
                decoration: const InputDecoration(
                  labelText: 'Transliteration (Optional)',
                  hintText: 'Enter the transliteration',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
                onChanged: (value) =>
                    _updateStep(index, transliteration: value),
              ),
              const SizedBox(height: 12),

              // Translation Field
              TextFormField(
                initialValue: step.translation,
                decoration: const InputDecoration(
                  labelText: 'Translation *',
                  hintText: 'Enter the English translation',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter translation';
                  }
                  return null;
                },
                onChanged: (value) => _updateStep(index, translation: value),
              ),
              const SizedBox(height: 12),

              // Target Count Field
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      initialValue: step.targetCount.toString(),
                      decoration: const InputDecoration(
                        labelText: 'Target Count *',
                        hintText: 'Number of repetitions',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter target count';
                        }
                        final count = int.tryParse(value);
                        if (count == null || count < 1) {
                          return 'Please enter a valid number (1 or more)';
                        }
                        return null;
                      },
                      onChanged: (value) {
                        final count = int.tryParse(value) ?? 1;
                        _updateStep(index, targetCount: count);
                      },
                    ),
                  ),
                  const SizedBox(width: 16),

                  // Quick count buttons
                  Wrap(
                    spacing: 4,
                    children: [1, 3, 7, 10, 33, 100].map((count) {
                      return ActionChip(
                        label: Text(count.toString()),
                        onPressed: () => _updateStep(index, targetCount: count),
                        backgroundColor: step.targetCount == count
                            ? Theme.of(
                                context,
                              ).colorScheme.primary.withValues(alpha: 0.2)
                            : null,
                      );
                    }).toList(),
                  ),
                ],
              ),

              // Template Selection (if available)
              if (_hasTemplates())
                Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: Row(
                    children: [
                      const Text('Quick Templates: '),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Wrap(
                          spacing: 4,
                          children: _getTemplateChips(index),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _updateStep(
    int index, {
    String? arabicText,
    String? transliteration,
    String? translation,
    int? targetCount,
  }) {
    final currentStep = _steps[index];
    final updatedStep = currentStep.copyWith(
      arabicText: arabicText ?? currentStep.arabicText,
      transliteration: transliteration ?? currentStep.transliteration,
      translation: translation ?? currentStep.translation,
      targetCount: targetCount ?? currentStep.targetCount,
    );

    setState(() {
      _steps[index] = updatedStep;
    });

    widget.onStepsChanged(_steps);
  }

  void _removeStep(int index) {
    if (_steps.length <= 1) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('At least one step is required')),
      );
      return;
    }

    setState(() {
      _steps.removeAt(index);
      _formKeys.removeAt(index);
    });

    widget.onStepsChanged(_steps);
  }

  void _reorderSteps(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final step = _steps.removeAt(oldIndex);
      _steps.insert(newIndex, step);

      final formKey = _formKeys.removeAt(oldIndex);
      _formKeys.insert(newIndex, formKey);
    });

    widget.onStepsChanged(_steps);
  }

  bool _hasTemplates() {
    return true; // For now, always show templates
  }

  List<Widget> _getTemplateChips(int index) {
    final templates = [
      {
        'name': 'Subhan Allah',
        'arabic': 'سُبْحَانَ اللَّهِ',
        'transliteration': 'Subhan Allah',
        'translation': 'Glory be to Allah',
        'count': 33,
      },
      {
        'name': 'Alhamdulillah',
        'arabic': 'الْحَمْدُ لِلَّهِ',
        'transliteration': 'Alhamdulillah',
        'translation': 'All praise is due to Allah',
        'count': 33,
      },
      {
        'name': 'Allahu Akbar',
        'arabic': 'اللَّهُ أَكْبَرُ',
        'transliteration': 'Allahu Akbar',
        'translation': 'Allah is the Greatest',
        'count': 34,
      },
      {
        'name': 'La hawla wa la quwwata illa billah',
        'arabic': 'لَا حَوْلَ وَلَا قُوَّةَ إِلَّا بِاللَّهِ',
        'transliteration': 'La hawla wa la quwwata illa billah',
        'translation': 'There is no power except with Allah',
        'count': 10,
      },
      {
        'name': 'Astaghfirullah',
        'arabic': 'أَسْتَغْفِرُ اللَّهَ',
        'transliteration': 'Astaghfirullah',
        'translation': 'I seek forgiveness from Allah',
        'count': 100,
      },
    ];

    return templates.map((template) {
      return ActionChip(
        label: Text(
          template['name'] as String,
          style: const TextStyle(fontSize: 12),
        ),
        onPressed: () => _applyTemplate(index, template),
        backgroundColor: Theme.of(
          context,
        ).colorScheme.secondary.withValues(alpha: 0.1),
      );
    }).toList();
  }

  void _applyTemplate(int index, Map<String, dynamic> template) {
    _updateStep(
      index,
      arabicText: template['arabic'] as String,
      transliteration: template['transliteration'] as String,
      translation: template['translation'] as String,
      targetCount: template['count'] as int,
    );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Applied template: ${template['name']}'),
        duration: const Duration(seconds: 1),
      ),
    );
  }
}
