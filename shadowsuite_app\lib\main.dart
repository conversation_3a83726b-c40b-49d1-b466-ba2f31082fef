import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'core/theme/app_theme.dart';
import 'core/providers/app_providers.dart';
import 'core/providers/theme_providers.dart' as theme_providers;
import 'core/services/sync_service.dart';
import 'core/services/supabase_service.dart';
import 'core/services/bug_reporter_service.dart';
import 'core/database/platform_database.dart';
import 'core/constants/app_constants.dart';
import 'core/localization/app_localizations.dart';
import 'presentation/screens/main_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize SharedPreferences
    final sharedPreferences = await SharedPreferences.getInstance();

    // Initialize Database
    final database = DatabaseFactory.getInstance();
    await database.initializeWithSampleData();

    // Initialize Supabase Service
    final supabaseService = SupabaseService();
    await supabaseService.initialize();

    // Initialize Bug Reporter
    final bugReporter = BugReporterService();
    await bugReporter.initialize();

    // Initialize Sync Service
    final syncService = SyncService();
    await syncService.initialize();

    runApp(
      ProviderScope(
        overrides: [
          sharedPreferencesProvider.overrideWithValue(sharedPreferences),
        ],
        child: const ShadowSuiteApp(),
      ),
    );
  } catch (e, stackTrace) {
    // If initialization fails, show error
    print('Initialization error: $e');
    print('Stack trace: $stackTrace');

    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                const Text(
                  'Failed to initialize app',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(
                  'Error: $e',
                  style: const TextStyle(fontSize: 14),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    // Restart the app
                    main();
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ShadowSuiteApp extends ConsumerWidget {
  const ShadowSuiteApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final locale = ref.watch(theme_providers.languageProvider);
    final currentTheme = ref.watch(theme_providers.currentThemeProvider);

    return MaterialApp(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,
      theme: currentTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode
          .light, // Always use light mode since we handle theme in currentTheme
      locale: locale,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: AppLocalizations.supportedLocales,
      home: const MainScreen(),
    );
  }
}
